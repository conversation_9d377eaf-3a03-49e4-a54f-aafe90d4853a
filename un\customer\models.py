# users/models.py

import json
import re
from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone

    
class Profile(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE,verbose_name='用户')
    moneys = models.IntegerField(default=0, verbose_name='余额')
    isvip = models.IntegerField(default=0, verbose_name='是否为会员')
    sex = models.IntegerField(default=0, verbose_name='性别',choices=[(0, '男'), (1, '女')])
    phone = models.CharField(max_length=20, verbose_name='手机号', default='未知')
    province = models.CharField(max_length=100, verbose_name='省份',default='未知')
    avatar = models.ImageField(upload_to='avatar', verbose_name='头像', default='avatar/default.jpg')
    def __str__(self):
        return self.user.username
    class Mate:
        verbose_name = '用户信息'
        verbose_name_plural = '用户信息'


class Category(models.Model):
    name = models.CharField(max_length=100, verbose_name='分类名称')
    def __str__(self):
        return self.name
    
class Book(models.Model):
    bid = models.CharField(max_length=100, verbose_name='图书编号', unique=True, blank=True, null=True)
    name = models.CharField(verbose_name="书名", max_length=30)
    author = models.CharField(verbose_name="作者", max_length=30)
    img_url = models.CharField(verbose_name="图片", max_length=300)
    biy = models.TextField(verbose_name="标语", max_length=200, blank=True, null=True)
    description = models.TextField(blank=True, null=True, verbose_name='图书简介')
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='图书价格')
    nf = models.CharField(verbose_name="出版年份", max_length=30)
    cbs = models.CharField(verbose_name="出版社", max_length=30)
    des = models.TextField(verbose_name="简介", max_length=500, blank=True, null=True)
    kcl = models.IntegerField(verbose_name="库存量", default=10)  
    content = models.TextField(verbose_name="内容",blank=True, null=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='products', verbose_name='图书分类')

    def __str__(self):
        return self.name
    class Meta:
        verbose_name = '图书'
        verbose_name_plural = '图书'

class Content(models.Model):
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='contents', verbose_name='图书')
    title = models.TextField(verbose_name='标题')
    content = models.TextField(verbose_name='内容', blank=True, null=True)
    def __str__(self):
        return self.title
    class Meta:
        verbose_name = '内容'
        verbose_name_plural = '内容'

class Bookcomment(models.Model):
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='comments', verbose_name='图书')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    content = models.TextField(verbose_name='评论内容', blank=True, null=True)
    create_time = models.DateTimeField(verbose_name='创建时间', auto_now_add=True)

    def __str__(self):
        return f"{self.user.username}: {self.content[:50]}"

    class Meta:
        verbose_name = '书评'
        verbose_name_plural = '书评'
        ordering = ['-create_time']


class Cart(models.Model): 
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户") 
    product= models.ForeignKey(Book, on_delete=models.CASCADE, verbose_name="图书") 
    count = models.IntegerField(verbose_name="图书数量", default=1) 
    def __str__(self): 
        return self.user.username 
    class Meta: 
        verbose_name = "购物车" 
        verbose_name_plural = verbose_name
    
class Favorite(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户")
    product = models.ForeignKey(Book, on_delete=models.CASCADE, verbose_name="图书")
    def __str__(self):
        return self.user.username
    class Meta:
        verbose_name = "收藏"
        verbose_name_plural = verbose_name



class Order(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户")
    status = models.IntegerField(choices=[(0, '未支付'), (1, '已支付')], default=0, verbose_name="订单状态")
    goods = models.TextField(verbose_name="图书", default='{}')  
    total = models.FloatField(verbose_name="图书总价", default=0)
    order_num = models.CharField(verbose_name="订单号", max_length=128)
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)

    def save(self, *args, **kwargs):
     
        self.order_num = f"{self.user.username}_{timezone.now().strftime('%Y%m%d%H%M%S')}"


        super().save(*args, **kwargs)
    def __str__(self):
        return self.order_num
    class Meta:
        verbose_name = "订单"
        verbose_name_plural = verbose_name


class Comment(models.Model):
    FEEDBACK_TYPES = (
        ('bug', '错误报告'),
        ('suggestion', '功能建议'),
        ('question', '问题咨询'),
        ('other', '其他反馈')
    )
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户")
    email = models.CharField(verbose_name="联系方式", blank=True, null=True, max_length=128)
    lx = models.CharField(verbose_name="反馈类型", max_length=128, choices=FEEDBACK_TYPES, default='other')
    cont = models.TextField(verbose_name="反馈内容", default='')
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    reply = models.TextField(verbose_name="回复内容", blank=True, null=True)
    
    def __str__(self):
        return f"{self.get_lx_display()} - {self.user.username}"
    
    class Meta:
        verbose_name = "用户反馈"
        verbose_name_plural = verbose_name
        ordering = ['-create_time']