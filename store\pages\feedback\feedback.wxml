<!-- pages/feedback/feedback.wxml -->
<view class="feedback-container">


  <view class="conr">
    <form bindsubmit="submitFeedback">
      <!-- 反馈类型选择 -->
      <view class="form-group">
        <text class="form-label">反馈类型</text>
        <picker class="picker" mode="selector" range="{{feedbackTypes}}" range-key="name" value="{{feedbackTypeIndex}}" bindchange="changeFeedbackType">
          <view class="picker-content">
            {{feedbackTypes[feedbackTypeIndex].name}}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="form-label">反馈内容</text>
        <textarea 
          class="textarea" 
          placeholder="请详细描述您的问题或建议..." 
          maxlength="500" 
          auto-height 
          bindinput="inputContent"
          value="{{content}}"
        ></textarea>
        <text class="word-count">{{content.length}}/500</text>
      </view>

      <view class="form-group">
        <text class="form-label">联系方式(选填)</text>
        <input 
          class="input" 
          type="text" 
          placeholder="邮箱/手机号" 
          bindinput="inputContact"
          value="{{contact}}"
        />
      </view>

      <button class="submit-btn" formType="submit" disabled="{{!content || isSubmitting}}">
        {{isSubmitting ? '提交中...' : '提交反馈'}}
      </button>
    </form>
  </view>

  <view class="history-link" bindtap="feedback">
    查看历史反馈记录
  </view>
</view>