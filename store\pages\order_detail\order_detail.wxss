/* 页面容器 */
.container {
  padding: 20rpx 0;
  background-color: #f7f7f7;
  align-items: center;

}

.oritme{
  width: 100%;
}
/* 订单信息卡片 */
.order-info {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  width: 100%;
}

/* 商品项卡片 */
.product-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  width: 96%;
}

/* 其余样式保持不变... */
.order-number {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f1f1f1;
  padding-bottom: 20rpx;
}

.order-total {
  display: block;
  font-size: 32rpx;
  color: #ff6b00;
  font-weight: bold;
  margin: 20rpx 0;
}

.order-status {
  display: inline-block;
  font-size: 26rpx;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  margin: 10rpx 0;
}

.order-status[data-status="0"] {
  background-color: #fff2e8;
  color: #ff6b00;
}

.order-status[data-status="1"] {
  background-color: #e8f8f0;
  color: #07c160;
}

.order-time {
  display: block;
  font-size: 24rpx;
  color: rgb(0, 0, 0);
  margin-top: 20rpx;
}

.goods-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin: 30rpx 0 20rpx;
  padding-left: 2%;
  width: 96%;
}

.product-item image {
  width: 120rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 25rpx;
  background-color: #f5f5f5;
}

.product-info {
  flex: 1;
}

.product-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: bold;
}

.product-price {
  display: block;
  font-size: 26rpx;
  color: #ff6b00;
}

.loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
  width: 100%;
}