/* 订单列表容器 */
.container {
  padding: 15rpx;
  background-color: #f7f7f7;
  width: 100%;
}

/* 单个订单项 */
.order-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
  width: 96%;
  margin: 0 2%; 
  margin-top: 1%;
}

/* 订单号样式 */
.order-number {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 15rpx;
}

/* 订单总价样式 */
.order-total {
  display: block;
  font-size: 30rpx;
  color: #ff6b00;
  font-weight: bold;
  margin: 10rpx 0;
}

/* 订单状态样式 */
.order-status {
  display: inline-block;
  font-size: 26rpx;
  padding: 6rpx 15rpx;
  border-radius: 20rpx;
  margin: 8rpx 0;
  background-color: #f1f1f1;
  color: #666;
}

/* 不同状态的颜色 */
.order-status[data-status="0"] {
  background-color: #fff2e8;
  color: #ff6b00;
}

.order-status[data-status="1"] {
  background-color: #e8f8f0;
  color: #07c160;
}

/* 订单时间样式 */
.order-time {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 15rpx;
}

/* 添加右侧箭头 */
.order-item::after {
  content: ">";
  position: absolute;
  right: 25rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #ccc;
  font-size: 32rpx;
}