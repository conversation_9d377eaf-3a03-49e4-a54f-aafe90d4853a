{% extends 'base.html' %}
{% block content %}
{% load static %}

<style>
.container1{
    max-width: 1200px;
    margin: 0 auto;
    margin-top: 100px;

}

    form {
    width: 100%;
    margin: 0 auto;
    padding: 20px;
    background-color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

form button {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 4px;
}

form button:hover {
    background-color: #45a049;
}

form input, form select, form textarea {
    width: 100%;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    border: 1px solid #ccc;
    box-sizing: border-box;
}

form input[type="file"] {
    padding: 5px;
}

form label {
    font-weight: bold;
}

.error-message {
    color: red;
    font-size: 14px;
    text-align: center;
    margin-bottom: 20px;
}

a {
    display: block;
    text-align: center;
    margin-top: 20px;
    text-decoration: none;
    font-size: 16px;
    color: #007bff;
}

a:hover {
    text-decoration: underline;
}
</style>

<div class="container1">
{% if error %}
        <div class="error-message">
            <p>{{ error }}</p>
        </div>
    {% endif %}
    
    <form method="POST" enctype="multipart/form-data">
        {% csrf_token %}
        {{ form.as_p }}
        <button type="submit">添加图书</button>
        
    <a href="{% url 'ahome' %}">返回列表</a>
    </form>



</div>

{% endblock %}