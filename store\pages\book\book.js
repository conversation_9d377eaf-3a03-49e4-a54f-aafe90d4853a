const app = getApp();
Page({
  data: {
    books: [],
    searchQuery: '',
    page: 1,
    pageSize: 10,
    hasMore: true,
    isLoading: false,
    total: 0
  },

  onLoad() {
    this.loadBooks()
  },

  // 加载图书数据
  loadBooks(reset = false) {
    if (this.data.isLoading) return
    
    this.setData({ isLoading: true })
    
    const { page, pageSize, searchQuery } = this.data
    const params = {
      page: reset ? 1 : page,
      page_size: pageSize
    }
    if (searchQuery) params.query = searchQuery

    wx.request({
      url: 'http://127.0.0.1:8000/books/',
      method: 'GET',
      data: params,
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success: (res) => {
        if (res.data.code === 1) {
          const newBooks = reset ? res.data.data.results : [...this.data.books, ...res.data.data.results]
          const total = res.data.data.count || 0
          
          this.setData({
            books: newBooks,
            total: total,
            hasMore: newBooks.length < total,
            page: reset ? 2 : page + 1
          })
        } else {
          wx.showToast({
            title: res.data.msg || '加载失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      },
      complete: () => {
        this.setData({ isLoading: false })
      }
    })
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({ searchQuery: e.detail.value })
  },

  // 点击搜索按钮
  onSearch() {
    this.setData({ books: [], hasMore: true })
    this.loadBooks(true)
  },

  // 回车搜索
  onSearchConfirm() {
    this.onSearch()
  },

  // 加载更多
  loadMore() {
    if (!this.data.hasMore || this.data.isLoading) return
    this.loadBooks()
  },

  // 跳转详情页
  navigateToDetail(e) {
    const bookId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/bookDetail/bookDetail?id=${bookId}`
    })
  }
})