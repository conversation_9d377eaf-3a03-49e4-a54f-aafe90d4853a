# Generated by Django 4.2.20 on 2025-06-16 08:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0002_alter_bookcomment_options_bookcomment_create_time_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='bookcomment',
            options={'verbose_name': '书评'},
        ),
        migrations.RemoveField(
            model_name='bookcomment',
            name='create_time',
        ),
        migrations.RemoveField(
            model_name='bookcomment',
            name='user',
        ),
        migrations.RemoveField(
            model_name='profile',
            name='address',
        ),
        migrations.AddField(
            model_name='profile',
            name='province',
            field=models.CharField(default='未知', max_length=100, verbose_name='省份'),
        ),
    ]
