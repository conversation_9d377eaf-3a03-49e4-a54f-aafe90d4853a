// pages/feedback/feedback.js
const app = getApp();
Page({
  data: {
    feedbackTypes: [
      { id: 'bug', name: '错误报告' },
      { id: 'suggestion', name: '功能建议' },
      { id: 'question', name: '问题咨询' },
      { id: 'other', name: '其他反馈' }
    ],
    feedbackTypeIndex: 0,
    content: '',
    contact: '',
    isSubmitting: false
  },

  // 选择反馈类型
  changeFeedbackType(e) {
    this.setData({
      feedbackTypeIndex: e.detail.value
    });
  },

  // 输入反馈内容
  inputContent(e) {
    this.setData({
      content: e.detail.value
    });
  },

  // 输入联系方式
  inputContact(e) {
    this.setData({
      contact: e.detail.value
    });
  },

  // 提交反馈
  submitFeedback() {
    const { content, feedbackTypes, feedbackTypeIndex, contact, isSubmitting } = this.data;
    
    if (!content.trim()) {
      wx.showToast({
        title: '请填写反馈内容',
        icon: 'none'
      });
      return;
    }

    if (isSubmitting) return;
    
    this.setData({ isSubmitting: true });
    
    // 调用API提交反馈
    wx.request({
      url: 'http://127.0.0.1:8000/feedback/',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      data: {
        feedback_type: feedbackTypes[feedbackTypeIndex].id,
        cont: content,
        email: contact
      },
      success: (res) => {
        if (res.statusCode === 201) {
          wx.showToast({
            title: '反馈提交成功',
            icon: 'success'
          });
          // 清空表单
          this.setData({
            content: '',
            contact: '',
            feedbackTypeIndex: 0
          });
        } else {
          throw new Error(res.data.message || '提交失败');
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isSubmitting: false });
      }
    });
  },
  feedback(){
    wx.navigateTo({
      url: '/pages/feedbacks/feedbacks',
    })
  }
});