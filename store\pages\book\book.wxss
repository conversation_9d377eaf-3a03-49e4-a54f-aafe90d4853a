/* 搜索框容器 */
.container {
  padding: 20rpx 30rpx;
  background-color: #f8f8f8;

}

/* 搜索框整体样式 */
.search-box {
  display: flex;
  align-items: center;
  height: 80rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
  border-radius: 40rpx;
  padding: 0 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #e0e3e6;

  margin: 0;
  width: 100%;
}

/* 输入框样式 */
.search-box input {
  flex: 1;
  height: 100%;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
}

.search-box input::placeholder {
  color: #999;
  font-size: 26rpx;
}

/* 搜索按钮样式 */
.search-box button {
  margin: 0;
  padding: 0 30rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 28rpx;
  color: #fff;
  background: linear-gradient(135deg, #36D1DC 0%, #5B86E5 100%);
  border-radius: 30rpx;
  border: none;
  box-shadow: 0 2rpx 6rpx rgba(58, 129, 229, 0.3);
}

.search-box button::after {
  border: none;
}

.search-box button:active {
  opacity: 0.9;
  transform: scale(0.98);
}

.book-item {
  display: flex;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}
.book-item image {
  width: 180rpx;
  height: 240rpx;
  border-radius: 8rpx;
}
.book-info {
  flex: 1;
  margin-left: 25rpx;
  display: flex;
  flex-direction: column;
}
.title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.author {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}
.price {
  color: #e64340;
  font-size: 32rpx;
  font-weight: bold;
}
.stock {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 100rpx;
}
.empty image {
  width: 300rpx;
  margin-bottom: 30rpx;
}
.empty text {
  color: #999;
}

.loading, .no-more {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 26rpx;
}