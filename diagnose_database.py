#!/usr/bin/env python3
"""
诊断数据库问题
"""
import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
sys.path.append('un')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'un.settings')
django.setup()

from customer.models import Book, User, Bookcomment
from django.contrib.auth import get_user_model
from django.db import connection

def diagnose_database():
    print("=== 数据库诊断 ===")
    
    # 1. 检查数据库连接
    print("1. 检查数据库连接...")
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            print("✅ 数据库连接正常")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return
    
    # 2. 检查表结构
    print("\n2. 检查Bookcomment表结构...")
    try:
        with connection.cursor() as cursor:
            cursor.execute("PRAGMA table_info(customer_bookcomment)")
            columns = cursor.fetchall()
            print("表字段:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
    
    # 3. 直接SQL查询最新评论
    print("\n3. 直接SQL查询最新评论...")
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT id, content, user_id, book_id, create_time 
                FROM customer_bookcomment 
                ORDER BY id DESC 
                LIMIT 10
            """)
            rows = cursor.fetchall()
            print(f"最新10条评论 (SQL查询):")
            for row in rows:
                print(f"  ID: {row[0]}, 内容: {row[1][:30]}..., 用户ID: {row[2]}, 图书ID: {row[3]}, 时间: {row[4]}")
    except Exception as e:
        print(f"❌ SQL查询失败: {e}")
    
    # 4. Django ORM查询
    print("\n4. Django ORM查询...")
    try:
        # 查询所有评论
        all_comments = Bookcomment.objects.all().order_by('-id')
        print(f"总评论数 (ORM): {all_comments.count()}")
        
        # 查询最新评论
        latest_comments = all_comments[:10]
        print("最新10条评论 (ORM):")
        for comment in latest_comments:
            user_name = comment.user.username if comment.user else "匿名"
            create_time = comment.create_time.strftime('%Y-%m-%d %H:%M:%S') if comment.create_time else "无时间"
            print(f"  ID: {comment.id}, 内容: {comment.content[:30]}..., 用户: {user_name}, 时间: {create_time}")
    except Exception as e:
        print(f"❌ ORM查询失败: {e}")
    
    # 5. 检查最近1小时的评论
    print("\n5. 检查最近1小时的评论...")
    try:
        from django.utils import timezone
        one_hour_ago = timezone.now() - timedelta(hours=1)
        recent_comments = Bookcomment.objects.filter(
            create_time__gte=one_hour_ago
        ).order_by('-create_time')
        
        print(f"最近1小时评论数: {recent_comments.count()}")
        for comment in recent_comments:
            user_name = comment.user.username if comment.user else "匿名"
            create_time = comment.create_time.strftime('%Y-%m-%d %H:%M:%S') if comment.create_time else "无时间"
            print(f"  ID: {comment.id}, 用户: {user_name}, 内容: {comment.content[:50]}..., 时间: {create_time}")
    except Exception as e:
        print(f"❌ 检查最近评论失败: {e}")
    
    # 6. 检查特定图书的评论
    print("\n6. 检查图书ID=1的评论...")
    try:
        book = Book.objects.get(id=1)
        book_comments = Bookcomment.objects.filter(book=book).order_by('-create_time')
        print(f"图书《{book.name}》的评论数: {book_comments.count()}")
        
        print("最新5条评论:")
        for comment in book_comments[:5]:
            user_name = comment.user.username if comment.user else "匿名"
            create_time = comment.create_time.strftime('%Y-%m-%d %H:%M:%S') if comment.create_time else "无时间"
            print(f"  ID: {comment.id}, 用户: {user_name}, 时间: {create_time}")
            print(f"    内容: {comment.content}")
    except Exception as e:
        print(f"❌ 检查特定图书评论失败: {e}")
    
    # 7. 检查数据库文件
    print("\n7. 检查数据库文件...")
    try:
        db_path = "un/db.sqlite3"
        if os.path.exists(db_path):
            stat = os.stat(db_path)
            print(f"✅ 数据库文件存在: {db_path}")
            print(f"   文件大小: {stat.st_size} bytes")
            print(f"   最后修改: {datetime.fromtimestamp(stat.st_mtime)}")
        else:
            print(f"❌ 数据库文件不存在: {db_path}")
    except Exception as e:
        print(f"❌ 检查数据库文件失败: {e}")

def test_comment_visibility():
    """测试评论的可见性"""
    print("\n=== 测试评论可见性 ===")
    
    # 创建一个测试评论
    try:
        book = Book.objects.get(id=1)
        user = User.objects.first()
        
        # 创建评论
        test_comment = Bookcomment.objects.create(
            book=book,
            user=user,
            content=f"测试评论 - {datetime.now()}"
        )
        
        print(f"✅ 创建测试评论，ID: {test_comment.id}")
        
        # 立即查询
        found_comment = Bookcomment.objects.filter(id=test_comment.id).first()
        if found_comment:
            print(f"✅ 立即查询到评论: {found_comment.content}")
        else:
            print("❌ 立即查询不到评论")
        
        # 查询该书的所有评论
        book_comments = Bookcomment.objects.filter(book=book).order_by('-id')
        print(f"✅ 该书总评论数: {book_comments.count()}")
        
        # 检查是否在列表中
        comment_ids = list(book_comments.values_list('id', flat=True))
        if test_comment.id in comment_ids:
            print(f"✅ 测试评论在列表中，位置: {comment_ids.index(test_comment.id) + 1}")
        else:
            print("❌ 测试评论不在列表中")
            
    except Exception as e:
        print(f"❌ 测试评论可见性失败: {e}")

if __name__ == "__main__":
    diagnose_database()
    test_comment_visibility()
