#!/usr/bin/env python3
"""
检查购物车记录
"""
import os
import sys
import django

# 设置Django环境
sys.path.append('un')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'un.settings')
django.setup()

from django.contrib.auth import get_user_model
from customer.models import Cart, Book

def check_cart_records():
    """检查购物车记录"""
    
    User = get_user_model()
    
    print("=== 购物车记录检查 ===")
    
    # 1. 显示最新的购物车记录
    print("1. 最新的10条购物车记录:")
    latest_carts = Cart.objects.all().order_by('-id')[:10]
    for cart in latest_carts:
        print(f"   ID: {cart.id} | 用户: {cart.user.username} | 商品: {cart.product.name} | 数量: {cart.count}")
    
    # 2. 按用户分组显示
    print("\n2. 按用户分组的购物车:")
    users = User.objects.all()
    for user in users:
        user_carts = Cart.objects.filter(user=user)
        if user_carts.exists():
            print(f"   用户 {user.username} ({user.id}):")
            for cart in user_carts:
                print(f"     - 购物车ID: {cart.id}, 商品: {cart.product.name}, 数量: {cart.count}")
        else:
            print(f"   用户 {user.username} ({user.id}): 购物车为空")
    
    # 3. 统计信息
    print(f"\n3. 统计信息:")
    total_carts = Cart.objects.count()
    total_users = User.objects.count()
    print(f"   总购物车记录数: {total_carts}")
    print(f"   总用户数: {total_users}")
    
    # 4. 检查是否有用户隔离问题
    print(f"\n4. 用户隔离检查:")
    for user in users:
        user_cart_count = Cart.objects.filter(user=user).count()
        print(f"   用户 {user.username}: {user_cart_count} 条购物车记录")

if __name__ == "__main__":
    check_cart_records()
