const app = getApp();
Page({
  data: {
    categories: [],
    books: [],
    activeCategoryId: null
  },

  onLoad() {
    this.loadData();
  },

  loadData(categoryId = null) {
  
    const baseUrl = 'http://127.0.0.1:8000';
    const apiPath = categoryId ? 
      `/api/books/categories/?category_id=${categoryId}` : 
      '/api/books/categories/';
    

    const fullUrl = baseUrl + apiPath;
    
    wx.request({
      url: fullUrl,
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success: (res) => {
        this.setData({
          categories: res.data.categories,
          books: res.data.books,
          activeCategoryId: categoryId
        });
      },
      fail: (err) => {
        console.error('请求失败:', err);
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    });
  },

  switchCategory(e) {
    const categoryId = e.currentTarget.dataset.id;
    this.loadData(categoryId);
  },

  navigateToDetail(e) {
    const bookId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/bookDetail/bookDetail?id=${bookId}`
    });
  }
});