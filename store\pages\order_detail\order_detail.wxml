<view class="container">
  <view wx:if="{{order}}" class="oritme">
    <view class="order-info" style="width: 100%;">
      <text class="order-number">订单号: {{order.order_num}}</text>
      <text class="order-total">总价: ¥{{order.total}}</text>
      <text class="order-status" data-status="{{order.status}}">状态: {{order.status === 0 ? '未支付' : '已支付'}}</text>
      <text class="order-time">创建时间: {{order.create_time}}</text>
    </view>

    <text class="goods-title">商品清单</text>
    
    <block wx:for="{{order.goods}}" wx:key="product_id">
      <view class="product-item">
        <image src="{{item.product_image}}" mode="aspectFill" bindtap="onProductClick" data-id="{{item.product_id}}"></image>
        <view class="product-info">
          <text class="product-name">{{item.product_name}}</text>
          <text class="product-price">单价: ¥{{item.product_price}}</text>
        </view>
      </view>
    </block>
  </view>
  <view wx:else class="loading">
    <text>加载中...</text>
  </view>
</view>