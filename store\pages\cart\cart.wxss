
.cart-list {
  margin-top: -150rpx;
  width: 90%;
 margin-right: 5%;

}

/* 单个购物车商品项 */
.cart-item {
  display: flex;
  align-items: center;
  padding: 25rpx;
  margin-bottom: 20rpx;
  background-color: rgb(255, 255, 255);
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.233);
  transition: all 0.3s ease;
  width: 100%;
}

/* 商品图片样式 */
.product-image {
  width: 150rpx;
  height: 200rpx;
  border-radius: 8rpx;
  margin-right: 25rpx;
}

/* 商品信息区域 */
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 商品名称 */
.product-name {
  font-size: 28rpx;
  color: rgb(0, 0, 0);
  font-weight: 500;
  margin-bottom: 15rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 商品价格 */
.product-price {
  font-size: 32rpx;
  color: #e93a2d;
  font-weight: bold;
}

/* 删除按钮 */
.btn.delete {
  width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 24rpx;
  color: #fff;
  background-color: #ff5b5b;
  border-radius: 30rpx;
  margin-left: 20rpx;
  padding: 0;
}

/* 按钮点击效果 */
.btn.delete:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 空购物车提示 */
.cart-empty {
  text-align: center;
  padding: 100rpx 0;
  color: rgb(0, 0, 0);
  font-size: 28rpx;
}

/* 添加动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.cart-item {
  animation: fadeIn 0.3s ease forwards;
}

.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 100rpx;
}

.empty-cart image {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 30rpx;
}

.empty-cart text {
  font-size: 28rpx;
  color: rgb(0, 0, 0);
  margin-bottom: 40rpx;
}

.btn.browse {
  width: 200rpx;
  background: #07c160;
  color: #fff;
}

.checkout-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  border-top: 1rpx solid #f1f1f1;
  z-index: 100;
}

.checkout-bar .total {
  display: flex;
  align-items: center;
}

.checkout-bar .total-price {
  font-size: 32rpx;
  color: #e4393c;
  margin-left: 10rpx;
}

.btn.checkout {
  width: 250rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: #07c160;
  color: #fff;
  font-size: 30rpx;
  border-radius: 40rpx;
  padding: 0;
  margin: 0;
}