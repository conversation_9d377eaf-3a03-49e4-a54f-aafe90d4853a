{"version": 3, "sources": ["docs.css", "docs/assets/less/syntax.less", "docs/assets/less/ie10-viewport-bug-workaround.less", "docs/assets/less/buttons.less", "docs/assets/less/booticon.less", "docs/assets/less/skip-link.less", "docs/assets/less/nav.less", "docs/assets/less/footer.less", "docs/assets/less/masthead.less", "docs/assets/less/page-header.less", "docs/assets/less/ads.less", "docs/assets/less/featurettes.less", "docs/assets/less/featured-sites.less", "docs/assets/less/demos.less", "docs/assets/less/sidebar.less", "docs/assets/less/examples.less", "docs/assets/less/callouts.less", "docs/assets/less/swatches.less", "docs/assets/less/team.less", "docs/assets/less/responsive-tests.less", "docs/assets/less/glyphicons.less", "docs/assets/less/customizer.less", "docs/assets/less/brand.less", "docs/assets/less/clipboard-js.less", "docs/assets/less/anchor.less", "docs/assets/less/algolia.less", "docs/assets/less/misc.less"], "names": [], "mappings": "AAAA;;;;;GAKG;ACHH;EAAO,uBAAA;CDMN;ACLD;EAAK,YAAA;CDQJ;ACPD;EAAO,YAAA;EAAa,uBAAA;CDWnB;ACVD;EAAK,YAAA;CDaJ;ACZD;EAAK,YAAA;CDeJ;ACdD;EAAM,YAAA;CDiBL;AChBD;EAAM,YAAA;CDmBL;AClBD;EAAM,YAAA;CDqBL;ACpBD;EAAM,YAAA;CDuBL;ACtBD;EAAM,uBAAA;EAAwB,uBAAA;CD0B7B;ACzBD;EAAM,mBAAA;CD4BL;AC3BD;EAAM,YAAA;CD8BL;AC7BD;EAAM,YAAA;CDgCL;AC/BD;EAAM,uBAAA;EAAwB,uBAAA;CDmC7B;AClCD;EAAM,YAAA;CDqCL;ACpCD;EAAM,YAAA;CDuCL;ACtCD;EAAM,YAAA;CDyCL;ACxCD;EAAM,YAAA;CD2CL;AC1CD;EAAM,YAAA;CD6CL;AC5CD;EAAM,YAAA;CD+CL;AC9CD;EAAM,YAAA;CDiDL;AChDD;EAAM,YAAA;CDmDL;AClDD;EAAM,YAAA;CDqDL;ACpDD;EAAM,YAAA;CDuDL;ACtDD;EAAK,YAAA;CDyDJ;ACxDD;EAAK,eAAA;CD2DJ;AC1DD;EAAM,eAAA;CD6DL;AC5DD;EAAM,YAAA;CD+DL;AC9DD;EAAM,YAAA;CDiEL;AChED;EAAM,YAAA;CDmEL;AClED;EAAM,YAAA;CDqEL;ACpED;EAAM,YAAA;CDuEL;ACtED;EAAM,YAAA;CDyEL;ACxED;EAAM,YAAA;CD2EL;AC1ED;EAAM,YAAA;CD6EL;AC5ED;EAAM,YAAA;CD+EL;AC9ED;EAAM,eAAA;CDiFL;AChFD;EAAM,YAAA;CDmFL;AClFD;EAAM,YAAA;CDqFL;ACpFD;EAAK,YAAA;CDuFJ;ACtFD;EAAM,YAAA;CDyFL;ACxFD;EAAM,YAAA;CD2FL;AC1FD;EAAM,YAAA;CD6FL;AC5FD;EAAM,YAAA;CD+FL;AC9FD;EAAM,YAAA;CDiGL;AChGD;EAAM,YAAA;CDmGL;AClGD;EAAM,mBAAA;EAAoB,YAAA;CDsGzB;ACrGD;EAAM,YAAA;CDwGL;ACvGD;EAAM,YAAA;CD0GL;ACzGD;EAAM,YAAA;CD4GL;AC3GD;EAAM,YAAA;CD8GL;AC7GD;EAAM,YAAA;CDgHL;AC/GD;EAAM,YAAA;CDkHL;ACjHD;EAAM,YAAA;CDoHL;ACnHD;EAAM,YAAA;CDsHL;ACrHD;EAAM,YAAA;CDwHL;ACvHD;EAAM,YAAA;CD0HL;ACzHD;EAAM,YAAA;CD4HL;AC3HD;EAAM,YAAA;CD8HL;AC7HD;EAAM,YAAA;CDgIL;AC9HD;;;EAEiB,YAAA;CDiIhB;AC1HD;EACE,kBAAA;EACA,oBAAA;EACA,0BAAA;EACA,0BAAA;EACA,mBAAA;CD4HD;AC1HD;EACE,WAAA;EACA,cAAA;EACA,iBAAA;EACA,mBAAA;EACA,oBAAA;EACA,8BAAA;EACA,UAAA;CD4HD;AC1HD;EACE,mBAAA;EACA,YAAA;CD4HD;AC1HD;EACE,sBAAA;EACA,oBAAA;CD4HD;ACzHD;EACE,YAAA;EACA,cAAA;EACA,0BAAA;EAAA,uBAAA;EAAA,sBAAA;EAAA,kBAAA;CD2HD;AACD;;;;GAIG;AEzNH;EAAoB,oBAAA;CF4NnB;AE1ND;EAAoB,oBAAA;CFgOnB;AG5OD;EACE,eAAA;EACA,8BAAA;EACA,sBAAA;CH8OD;AG5OC;;;EAGE,YAAA;EACA,0BAAA;EACA,sBAAA;CH8OH;AGzOD;EACE,YAAA;EACA,8BAAA;EACA,sBAAA;CH2OD;AGzOC;;;EAGE,eAAA;EACA,kBAAA;EACA,uBAAA;EACA,mBAAA;CH2OH;AItQD;EACE,eAAA;EACA,iBAAA;EACA,YAAA;EACA,mBAAA;EACA,gBAAA;EACA,0BAAA;EACA,mBAAA;CJwQD;AIrQD;EACE,YAAA;EACA,aAAA;EACA,gBAAA;EACA,kBAAA;CJuQD;AIpQD;EACE,aAAA;EACA,cAAA;EACA,iBAAA;EACA,mBAAA;CJsQD;AInQD;EACE,eAAA;EACA,uBAAA;CJqQD;AIlQD;EACE,8BAAA;EACA,0BAAA;CJoQD;AKjSD;EACE,eAAA;EACA,aAAA;EACA,YAAA;EACA,0BAAA;EACA,WAAA;CLmSD;AKhSD;EACE,eAAA;EACA,oBAAA;CLkSD;AK/RD;EACE,cAAA;CLiSD;AMjTD;EACE,iBAAA;EACA,uBAAA;EACA,iBAAA;CNmTD;AMtTD;EAMI,cAAA;CNmTH;AMzTD;;EAWI,iBAAA;EACA,eAAA;CNkTH;AM9TD;EAiBM,oBAAA;EACA,mBAAA;CNgTL;AMlUD;;;EAwBM,eAAA;EACA,0BAAA;CN+SL;AMxUD;EA8BI,0BAAA;CN6SH;AM3UD;EAmCM,mBAAA;CN2SL;AMzSK;;EAEE,0BAAA;EACA,sBAAA;CN2SP;AMrSG;EAAA;IACE,cAAA;GNwSH;CACF;AOxVD;EACE,kBAAA;EACA,qBAAA;EACA,kBAAA;EACA,eAAA;EACA,mBAAA;EACA,0BAAA;CP0VD;AOxVD;EACE,YAAA;CP0VD;AOxVD;EACE,gBAAA;EACA,oBAAA;CP0VD;AOxVD;EACE,sBAAA;CP0VD;AOxVD;EACE,kBAAA;CP0VD;AOvVD;EACE;IACE,iBAAA;GPyVD;EOvVD;IACE,iBAAA;GPyVD;CACF;AQnXD;;EAEE,mBAAA;EACA,gBAAA;EACA,eAAA;EACA,mBAAA;EACA,wCAAA;EACA,0BAAA;EACA,8FAAA;EACA,yEAAA;EACA,oEAAA;EACA,uEAAA;EACA,mHAAA;EACA,4BAAA;CRqXD;AQjXD;EACE,oBAAA;CRmXD;AQjXD;EACE,iBAAA;EACA,eAAA;EACA,YAAA;CRmXD;AQjXD;EACE,oBAAA;EACA,gBAAA;EACA,YAAA;CRmXD;AQjXD;EACE,kBAAA;EACA,oBAAA;EACA,eAAA;CRmXD;AQjXD;EACE,YAAA;EACA,mBAAA;EACA,gBAAA;CRmXD;AQhXD;EACE;IACE,YAAA;GRkXD;CACF;AQ/WD;EACE;IACE,gBAAA;GRiXD;EQ/WD;IACE,gBAAA;GRiXD;EQ/WD;IACE,gBAAA;GRiXD;CACF;AQ9WD;EACE;IACE,WAAA;IACA,gBAAA;GRgXD;CACF;ASjbD;EACE,oBAAA;EACA,gBAAA;CTmbD;ASjbD;EACE,cAAA;EACA,YAAA;CTmbD;ASjbD;EACE,iBAAA;EACA,iBAAA;EACA,iBAAA;CTmbD;ASjbD;EACE,mBAAA;CTmbD;AShbD;EACE;IACE,kBAAA;IACA,qBAAA;IACA,gBAAA;IACA,iBAAA;GTkbD;EShbD;IACE,gBAAA;IACA,eAAA;GTkbD;CACF;AS/aD;EACE;;IAEE,oBAAA;GTibD;CACF;AUjdD;EACE,eAAA;EACA,8BAAA;EACA,yBAAA;EACA,iBAAA;EACA,gBAAA;EACA,iBAAA;EACA,iBAAA;EACA,sBAAA;EACA,sBAAA;CVmdD;AU5dD;EAYI,YAAA;EACA,sBAAA;CVmdH;AUhdC;EAAA;IACE,iBAAA;IACA,oBAAA;IACA,kBAAA;IACA,mBAAA;GVmdD;CACF;AUjdC;EAAA;IACE,mBAAA;IACA,OAAA;IACA,YAAA;IACA,cAAA;GVodD;EUldC;IACE,iBAAA;GVodH;CACF;AUhdD;EACE,YAAA;EACA,oBAAA;CVkdD;AU/cD;EACE,eAAA;EACA,0BAAA;CVidD;AW7fD;EACE,kBAAA;EACA,qBAAA;EACA,gBAAA;EACA,iBAAA;EACA,YAAA;EACA,mBAAA;EACA,uBAAA;EACA,iCAAA;CX+fD;AW7fD;EACE,cAAA;EACA,cAAA;CX+fD;AW5fD;EACE,mBAAA;EACA,gBAAA;EACA,iBAAA;EACA,YAAA;CX8fD;AW5fD;EACE,aAAA;EACA,kBAAA;CX8fD;AW5fD;EACE,mBAAA;EACA,iBAAA;EACA,YAAA;CX8fD;AW5fD;EACE,eAAA;EACA,oBAAA;EACA,YAAA;CX8fD;AW5fD;EACE,eAAA;EACA,sBAAA;CX8fD;AW5fD;EACE,eAAA;EACA,oBAAA;CX8fD;AW3fD;EACE;IACE,iBAAA;GX6fD;CACF;AW3fD;EACE;IACE,mBAAA;IACA,sBAAA;GX6fD;EW3fD;IACE,gBAAA;GX6fD;EW3fD;IACE,eAAA;IACA,mBAAA;IACA,kBAAA;GX6fD;EW3fD;IACE,cAAA;GX6fD;CACF;AY7jBD;EACE,mBAAA;EACA,kBAAA;CZ+jBD;AY7jBD;EACE,aAAA;CZ+jBD;AY7jBD;EACE,cAAA;CZ+jBD;AY5jBD;EACE;IACE,4BAAA;IACA,+BAAA;GZ8jBD;EY5jBD;IACE,6BAAA;IACA,gCAAA;GZ8jBD;CACF;AajlBD;EAEI,oBAAA;CbklBH;AaplBD;EAKO,mBAAA;CbklBN;AavlBD;EAOM,oBAAA;CbmlBL;AajlBC;EAAA;IACE,oBAAA;IACA,mBAAA;GbolBD;EatlBD;IAKI,oBAAA;IACA,mBAAA;GbolBH;CACF;AcjmBD;EACE,iBAAA;CdmmBD;AcjmBD;EACE;IACE,mBAAA;GdmmBD;CACF;AchmBD;EACE,oBAAA;EACA,kBAAA;CdkmBD;Ac9lBD;EACE,iBAAA;EACA,oBAAA;CdgmBD;Ac5lBD;EACE,eAAA;EACA,kBAAA;EACA,gBAAA;EACA,iBAAA;EACA,eAAA;Cd8lBD;Ac5lBD;;EAEE,mBAAA;EACA,eAAA;EACA,sBAAA;EACA,8BAAA;EACA,+BAAA;Cd8lBD;Ac5lBD;;;EAGE,mBAAA;EACA,iBAAA;EACA,eAAA;EACA,8BAAA;EACA,+BAAA;Cd8lBD;Ac1lBD;EACE,cAAA;EACA,qBAAA;Cd4lBD;Ac1lBD;EACE,iBAAA;EACA,oBAAA;EACA,mBAAA;EACA,gBAAA;EACA,iBAAA;Cd4lBD;Ac1lBD;;EAEE,mBAAA;Cd4lBD;Ac1lBD;;;EAGE,mBAAA;EACA,iBAAA;Cd4lBD;AcxlBD;;EAEE,cAAA;EACA,kBAAA;EACA,iBAAA;EACA,kBAAA;EACA,gBAAA;EACA,iBAAA;EACA,YAAA;Cd0lBD;AcxlBD;;EAEE,eAAA;EACA,sBAAA;Cd0lBD;AcxlBD;EACE,cAAA;Cd0lBD;AcvlBD;EACE;;IAEE,eAAA;GdylBD;CACF;AcrlBD;EACE;IACE,eAAA;GdulBD;EcplBD;;IAEE,aAAA;GdslBD;EcplBD;IACE,gBAAA;IACA,UAAA;GdslBD;EcplBD;IACE,mBAAA;GdslBD;EcplBD;;IAEE,cAAA;IACA,iBAAA;GdslBD;CACF;AcplBD;EAEE;;IAEE,aAAA;GdqlBD;CACF;Ae7sBD;EACE,oBAAA;Cf+sBD;Ae7sBD;EACE,kBAAA;EACA,qBAAA;EACA,uBAAA;EACA,0CAAA;EACA,uBAAA;EACA,yCAAA;Cf+sBD;AevsBD;EACE,mBAAA;EACA,wBAAA;EACA,qBAAA;EACA,gCAAA;EACA,oBAAA;EACA,oBAAA;EACA,wDAAA;EAAA,gDAAA;CfysBD;AetsBD;EACE,mBAAA;EACA,UAAA;EACA,WAAA;EACA,gBAAA;EACA,iBAAA;EACA,eAAA;EACA,0BAAA;EACA,oBAAA;EACA,mBAAA;CfwsBD;AersBD;EACE,qBAAA;CfusBD;AensBD;;EAEE,yBAAA;EACA,sBAAA;EACA,iBAAA;CfqsBD;AejsBD;EACE;IACE,gBAAA;IACA,eAAA;IACA,uBAAA;IACA,mBAAA;IACA,kBAAA;IACA,2BAAA;IACA,yBAAA;IAAA,iBAAA;GfmsBD;EejsBD;;IAEE,kBAAA;IACA,gBAAA;IACA,eAAA;IACA,kBAAA;IACA,gCAAA;IACA,+BAAA;GfmsBD;EejsBD;IACE,WAAA;IACA,2BAAA;GfmsBD;EejsBD;IACE,mBAAA;GfmsBD;CACF;Ae/rBD;EACE,YAAA;CfisBD;Ae7rBD;;;;;;;;;;;;;;EAcE,iBAAA;Cf+rBD;Ae7rBD;EACE,YAAA;Cf+rBD;Ae3rBD;EACE,eAAA;EACA,uBAAA;Cf6rBD;Ae3rBD;EACE,gBAAA;EACA,mBAAA;Cf6rBD;Ae3rBD;EACE,cAAA;Cf6rBD;Ae3rBD;;;;;;EAME,UAAA;Cf6rBD;AezrBD;EACE,cAAA;Cf2rBD;AevrBD;;;EAGE,YAAA;CfyrBD;AerrBD;EACE,uBAAA;CfurBD;AenrBD;;EAEE,gBAAA;EACA,mBAAA;CfqrBD;AenrBD;EACE,iBAAA;CfqrBD;AejrBD;;EAGI,iBAAA;CfkrBH;Ae/qBD;EACE,oBAAA;CfirBD;Ae/qBD;EACE,iBAAA;CfirBD;Ae7qBD;EACE,iBAAA;Cf+qBD;Ae3qBD;EACE,iBAAA;Cf6qBD;Ae3qBD;;EAEE,WAAA;EACA,WAAA;EACA,iBAAA;Cf6qBD;Ae3qBD;;EAEE,eAAA;Cf6qBD;Ae3qBD;;EAEE,mBAAA;EACA,gBAAA;EACA,eAAA;Cf6qBD;Ae3qBD;EACE,qBAAA;Cf6qBD;Ae3qBD;EACE,UAAA;EACA,aAAA;Cf6qBD;Ae3qBD;EACE,UAAA;Cf6qBD;Ae3qBD;EACE,kBAAA;Cf6qBD;Ae3qBD;EACE,aAAA;Cf6qBD;Ae3qBD;EACE,iBAAA;Cf6qBD;Ae3qBD;EACE;;IAEE,mBAAA;Gf6qBD;CACF;AezqBD;EACE,iBAAA;EACA,oBAAA;Cf2qBD;AevqBD;EACE,cAAA;CfyqBD;AerqBD;EACE,0BAAA;CfuqBD;AerqBD;EACE,mBAAA;EACA,UAAA;EACA,YAAA;EACA,aAAA;EACA,WAAA;EACA,WAAA;EACA,eAAA;CfuqBD;AerqBD;EACE,WAAA;EACA,mBAAA;EACA,kBAAA;CfuqBD;AenqBD;EACE,YAAA;CfqqBD;AenqBD;EACE,iBAAA;EACA,eAAA;EACA,mBAAA;EACA,YAAA;CfqqBD;AejqBD;EACE,oBAAA;CfmqBD;Ae/pBD;EACE,mBAAA;CfiqBD;Ae/pBD;EACE,gBAAA;EACA,mBAAA;CfiqBD;Ae/pBD;EACE,mBAAA;EACA,sBAAA;EACA,kBAAA;EACA,WAAA;CfiqBD;Ae7pBD;EACE,qBAAA;EACA,0BAAA;Cf+pBD;Ae7pBD;EACE,mBAAA;EACA,eAAA;EACA,YAAA;EACA,aAAA;EACA,aAAA;Cf+pBD;Ae3pBD;EACE,mBAAA;EACA,cAAA;EACA,iBAAA;EACA,eAAA;Cf6pBD;Ae1pBD;EACE,iBAAA;Cf4pBD;AexpBD;EACE,iBAAA;Cf0pBD;AetpBD;EACE,sBAAA;EACA,sCAAA;EACA,WAAA;EACA,wBAAA;EACA,oDAAA;EAAA,4CAAA;CfwpBD;AgBt9BD;EACE,cAAA;EACA,eAAA;EACA,uBAAA;EACA,uBAAA;EACA,mBAAA;ChBw9BD;AgB79BD;EAQI,cAAA;EACA,mBAAA;ChBw9BH;AgBj+BD;EAaI,iBAAA;ChBu9BH;AgBp+BD;EAiBI,mBAAA;ChBs9BH;AgBv+BD;EAqBI,iBAAA;ChBq9BH;AgBj9BD;EACE,2BAAA;ChBm9BD;AgBp9BD;EAII,eAAA;ChBm9BH;AgB/8BD;EACE,2BAAA;ChBi9BD;AgBl9BD;EAII,eAAA;ChBi9BH;AgB78BD;EACE,2BAAA;ChB+8BD;AgBh9BD;EAII,eAAA;ChB+8BH;AiBjgCD;EACE,eAAA;EACA,iBAAA;EjBmgCA,cAAc;CACf;AiBlgCD;EACE,YAAA;EACA,YAAA;EACA,aAAA;EACA,cAAA;EACA,mBAAA;CjBogCD;AiBjgCD;EACE;IACE,aAAA;IACA,cAAA;GjBmgCD;CACF;AiB//BD;EACE,uBAAA;CjBigCD;AiB//BD;EACE,uBAAA;CjBigCD;AiB//BD;EACE,uBAAA;CjBigCD;AiB//BD;EACE,uBAAA;CjBigCD;AiB//BD;EACE,uBAAA;CjBigCD;AiB//BD;EACE,0BAAA;CjBigCD;AiB//BD;EACE,0BAAA;CjBigCD;AiB//BD;EACE,0BAAA;CjBigCD;AiB//BD;EACE,0BAAA;CjBigCD;AiB//BD;EACE,0BAAA;CjBigCD;AiB7/BD;EACE,0BAAA;CjB+/BD;AiB7/BD;EACE,0BAAA;CjB+/BD;AiB7/BD;EACE,0BAAA;CjB+/BD;AiB7/BD;EACE,0BAAA;CjB+/BD;AkB7jCD;EACE,kBAAA;EACA,YAAA;ClB+jCD;AkB7jCD;EACE,YAAA;EACA,sBAAA;ClB+jCD;AkB7jCD;EACE,aAAA;EACA,aAAA;EACA,aAAA;EACA,gBAAA;EACA,aAAA;ClB+jCD;AkB7jCD;EACE,YAAA;EACA,YAAA;EACA,mBAAA;EACA,mBAAA;ClB+jCD;AmB/kCD;EACE,oBAAA;CnBilCD;AmB7kCD;;EAEE,eAAA;EACA,iBAAA;EACA,YAAA;CnB+kCD;AmB7kCD;EACE,iBAAA;CnB+kCD;AmB7kCD;EACE,mBAAA;CnB+kCD;AmB7kCD;EACE,eAAA;EACA,qCAAA;CnB+kCD;AmB7kCD;EACE,YAAA;EACA,qCAAA;CnB+kCD;AmB3kCD;EACE,gBAAA;CnB6kCD;AmB3kCD;EACE,oBAAA;CnB6kCD;AmB3kCD;EACE,eAAA;EACA,mBAAA;EACA,gBAAA;EACA,iBAAA;EACA,iBAAA;EACA,mBAAA;EACA,mBAAA;CnB6kCD;AmB3kCD;;;;;;;;EAQE,YAAA;EACA,uBAAA;CnB6kCD;AmB3kCD;;;;;;;;EAQE,eAAA;EACA,0BAAA;EACA,0BAAA;CnB6kCD;AoB/oCD;EACE,qBAAA;EACA,iBAAA;CpBipCD;AoB/oCD;EACE,gBAAA;EACA,iBAAA;CpBipCD;AoB/oCD;EACE,YAAA;EACA,WAAA;EACA,cAAA;EACA,cAAA;EACA,gBAAA;EACA,iBAAA;EACA,mBAAA;EACA,0BAAA;EACA,uBAAA;CpBipCD;AoB/oCD;EACE,gBAAA;EACA,oBAAA;EACA,gBAAA;CpBipCD;AoB/oCD;EACE,eAAA;EACA,mBAAA;EACA,sBAAA;CpBipCD;AoB/oCD;EACE,YAAA;EACA,0BAAA;CpBipCD;AoB9oCD;EACE;IACE,gBAAA;IACA,eAAA;GpBgpCD;EoB9oCD;IACE,aAAA;IACA,gBAAA;GpBgpCD;CACF;AqBxrCD;EACE,aAAA;EACA,iBAAA;CrB0rCD;AqBtrCD;EACE,iBAAA;EACA,iBAAA;EACA,YAAA;CrBwrCD;AqBtrCD;EACE,kBAAA;EACA,cAAA;EACA,mBAAA;CrBwrCD;AqBtrCD;EACE,iBAAA;CrBwrCD;AqBtrCD;EACE,iBAAA;EACA,iBAAA;CrBwrCD;AqBtrCD;EACE,cAAA;EACA,mBAAA;CrBwrCD;AqBtrCD;EACE,+DAAA;EACA,0BAAA;CrBwrCD;AqBtrCD;EACE,mBAAA;EACA,gBAAA;CrBwrCD;AqBprCD;EACE,iBAAA;CrBsrCD;AqBlrCD;EACE,cAAA;CrBorCD;AqBhrCD;EACE,gBAAA;EACA,OAAA;EACA,SAAA;EACA,QAAA;EACA,cAAA;EACA,gBAAA;EACA,YAAA;EACA,0BAAA;EACA,iCAAA;EACA,4DAAA;EAAA,oDAAA;CrBkrCD;AqBhrCD;EACE,iBAAA;EACA,gBAAA;CrBkrCD;AqBhrCD;EACE,iBAAA;CrBkrCD;AqBhrCD;EACE,kBAAA;CrBkrCD;AqBhrCD;EACE,iBAAA;EACA,YAAA;EACA,0BAAA;EACA,sBAAA;EACA,0FAAA;EAAA,kFAAA;CrBkrCD;AqB/qCD;EACE,mBAAA;EACA,cAAA;EACA,oBAAA;EACA,YAAA;EACA,mBAAA;EACA,wBAAA;EACA,mBAAA;CrBirCD;AqB/qCD;EACE,mBAAA;CrBirCD;AqB/qCD;EACE,gBAAA;CrBirCD;AqB/qCD;EACE,aAAA;CrBirCD;AqB/qCD;EACE,oBAAA;EACA,iBAAA;EACA,YAAA;CrBirCD;AqB/qCD;EACE,gBAAA;CrBirCD;AqB/qCD;EACE,iBAAA;CrBirCD;AsB1xCD;EACE,eAAA;EACA,YAAA;EACA,oBAAA;EACA,iBAAA;EACA,eAAA;EACA,0BAAA;EACA,mBAAA;CtB4xCD;AsBxxCD;EACE,gBAAA;EACA,mBAAA;CtB0xCD;AsBxxCD;EACE,2BAAA;CtB0xCD;AsBxxCD;EACE,YAAA;EACA,0BAAA;CtB0xCD;AsBtxCD;;EAEE,cAAA;EACA,iBAAA;CtBwxCD;AsBtxCD;EACE,mBAAA;EACA,kBAAA;CtBwxCD;AsBpxCD;EACE,YAAA;EACA,aAAA;EACA,wBAAA;EACA,kBAAA;EACA,YAAA;EACA,mBAAA;CtBsxCD;AsBpxCD;EACE,0BAAA;CtBsxCD;AsBpxCD;EACE,0BAAA;CtBsxCD;AsBnxCD;EACE;IACE,oBAAA;IACA,UAAA;GtBqxCD;EsBnxCD;IACE,cAAA;IACA,4BAAA;GtBqxCD;EsBnxCD;IACE,gBAAA;GtBqxCD;CACF;AuB/0CD;EACE,mBAAA;EACA,cAAA;EACA,aAAA;CvBi1CD;AuBp1CD;EAMI,cAAA;CvBi1CH;AuB70CD;EACE,mBAAA;EACA,OAAA;EACA,SAAA;EACA,YAAA;EACA,eAAA;EACA,iBAAA;EACA,gBAAA;EACA,eAAA;EACA,gBAAA;EACA,8BAAA;EACA,UAAA;EACA,6BAAA;EACA,+BAAA;CvB+0CD;AuB70CC;EACE,YAAA;EACA,0BAAA;CvB+0CH;AuB30CD;EACE;IACE,eAAA;GvB60CD;CACF;AwBn3CD;EACE,eAAA;CxBq3CD;AwBl3CD;EACE;IACE,cAAA;GxBo3CD;CACF;AwBj3CD;EACE,cAAA;EACA,uCAAA;EAAA,kCAAA;EAAA,+BAAA;CxBm3CD;AwBh3CD;;EAEE,sBAAA;EACA,WAAA;CxBk3CD;AyB/3CD;EACE,0BAAA;CzBi4CD;AyBl4CD;EAKI,YAAA;EACA,wBAAA;EACA,2BAAA;EACA,2BAAA;EACA,uBAAA;EACA,6BAAA;EACA,uBAAA;EACA,qCAAA;EACA,oDAAA;EAAA,4CAAA;CzBg4CH;AyB93CG;EAAA;IACE,YAAA;GzBi4CH;CACF;AyB93CG;EACE,yBAAA;CzBg4CL;AyBr5CD;EAyBM,sBAAA;EACA,6BAAA;EACA,yCAAA;EACA,qBAAA;CzB+3CL;AyB35CD;EAgCM,yBAAA;CzB83CL;AyB95CD;EAoCM,yBAAA;EAAA,iBAAA;CzB63CL;AyBj6CD;EAyCI,sBAAA;EACA,6BAAA;CzB23CH;AyBr6CD;EA8CI,6BAAA;EACA,yBAAA;EACA,2BAAA;EACA,4BAAA;EACA,0BAAA;EACA,4BAAA;CzB03CH;AyB76CD;EAuDI,uBAAA;EACA,0BAAA;CzBy3CH;AyBj7CD;EA6DI,uBAAA;EACA,uBAAA;EACA,sBAAA;EACA,4BAAA;CzBu3CH;AyBv7CD;EAoEI,uBAAA;EACA,uBAAA;EACA,sBAAA;CzBs3CH;AyBn3CG;EACE,yBAAA;CzBq3CL;AyBh3CG;EAEI,6BAAA;EACA,4BAAA;EACA,2BAAA;CzBi3CP;AyBp8CD;EAwFM,yBAAA;CzB+2CL;AyBv8CD;EA6FI,eAAA;EACA,6BAAA;EACA,4BAAA;EACA,2BAAA;EACA,4BAAA;CzB62CH;AyB98CD;EAqGI,+BAAA;EACA,iBAAA;EACA,2BAAA;EACA,iBAAA;EACA,6BAAA;CzB42CH;AyBr9CD;EA6GI,uBAAA;EACA,uBAAA;EACA,wBAAA;EACA,qBAAA;EACA,2BAAA;EACA,0BAAA;EACA,0BAAA;EACA,2BAAA;CzB22CH;AyB/9CD;EAwHI,2BAAA;EACA,6BAAA;EACA,0BAAA;EACA,0BAAA;EACA,4BAAA;CzB02CH;AyBt+CD;EAgII,eAAA;EACA,uBAAA;CzBy2CH;AyB1+CD;EAqII,uEAAA;EAAA,+DAAA;CzBw2CH;AyB7+CD;EAyII,qCAAA;CzBu2CH;A0B9+CD;EACE,mBAAA;C1Bg/CD;A0B5+CD;EACE,gBAAA;EACA,iBAAA;C1B8+CD;A0B1+CD;;;EAGE,0BAAA;C1B4+CD;A0Bx+CD;EACE,oBAAA;C1B0+CD;A0Bx+CD;EACE,iBAAA;C1B0+CD;A0Bv+CD;EACE,kBAAA;EACA,cAAA;C1By+CD;A0Br+CD;EACE,iBAAA;C1Bu+CD;A0Bp+CD;EACE,WAAA;C1Bs+CD;A0Bl+CD;;EAEE,oBAAA;C1Bo+CD;A0Bj+CD;EACE,aAAA;C1Bm+CD;A0Bh+CD;;EAEE,aAAA;C1Bk+CD;A0B/9CD;EACE,YAAA;C1Bi+CD;A0B79CD;EACE,eAAA;EACA,mBAAA;EACA,iBAAA;EACA,YAAA;EACA,mBAAA;EACA,0BAAA;C1B+9CD;A0B79CC;;EAEE,YAAA;EACA,sBAAA;EACA,0BAAA;C1B+9CH;AACD,uDAAuD;A0B39CvD;EACE;IACE,uBAAA;G1B69CD;CACF", "file": "docs.css", "sourcesContent": ["/*!\n * Bootstrap Docs (https://getbootstrap.com/)\n * Copyright 2011-2019 Twitter, Inc.\n * Licensed under the Creative Commons Attribution 3.0 Unported License. For\n * details, see https://creativecommons.org/licenses/by/3.0/.\n */\n.hll {\n  background-color: #ffc;\n}\n.c {\n  color: #999;\n}\n.err {\n  color: #a00;\n  background-color: #faa;\n}\n.k {\n  color: #069;\n}\n.o {\n  color: #555;\n}\n.cm {\n  color: #999;\n}\n.cp {\n  color: #099;\n}\n.c1 {\n  color: #999;\n}\n.cs {\n  color: #999;\n}\n.gd {\n  background-color: #fcc;\n  border: 1px solid #c00;\n}\n.ge {\n  font-style: italic;\n}\n.gr {\n  color: #f00;\n}\n.gh {\n  color: #030;\n}\n.gi {\n  background-color: #cfc;\n  border: 1px solid #0c0;\n}\n.go {\n  color: #aaa;\n}\n.gp {\n  color: #009;\n}\n.gu {\n  color: #030;\n}\n.gt {\n  color: #9c6;\n}\n.kc {\n  color: #069;\n}\n.kd {\n  color: #069;\n}\n.kn {\n  color: #069;\n}\n.kp {\n  color: #069;\n}\n.kr {\n  color: #069;\n}\n.kt {\n  color: #078;\n}\n.m {\n  color: #f60;\n}\n.s {\n  color: #d44950;\n}\n.na {\n  color: #4f9fcf;\n}\n.nb {\n  color: #366;\n}\n.nc {\n  color: #0a8;\n}\n.no {\n  color: #360;\n}\n.nd {\n  color: #99f;\n}\n.ni {\n  color: #999;\n}\n.ne {\n  color: #c00;\n}\n.nf {\n  color: #c0f;\n}\n.nl {\n  color: #99f;\n}\n.nn {\n  color: #0cf;\n}\n.nt {\n  color: #2f6f9f;\n}\n.nv {\n  color: #033;\n}\n.ow {\n  color: #000;\n}\n.w {\n  color: #bbb;\n}\n.mf {\n  color: #f60;\n}\n.mh {\n  color: #f60;\n}\n.mi {\n  color: #f60;\n}\n.mo {\n  color: #f60;\n}\n.sb {\n  color: #c30;\n}\n.sc {\n  color: #c30;\n}\n.sd {\n  font-style: italic;\n  color: #c30;\n}\n.s2 {\n  color: #c30;\n}\n.se {\n  color: #c30;\n}\n.sh {\n  color: #c30;\n}\n.si {\n  color: #a00;\n}\n.sx {\n  color: #c30;\n}\n.sr {\n  color: #3aa;\n}\n.s1 {\n  color: #c30;\n}\n.ss {\n  color: #fc3;\n}\n.bp {\n  color: #366;\n}\n.vc {\n  color: #033;\n}\n.vg {\n  color: #033;\n}\n.vi {\n  color: #033;\n}\n.il {\n  color: #f60;\n}\n.css .o,\n.css .o + .nt,\n.css .nt + .nt {\n  color: #999;\n}\n.highlight {\n  padding: 9px 14px;\n  margin-bottom: 14px;\n  background-color: #f7f7f9;\n  border: 1px solid #e1e1e8;\n  border-radius: 4px;\n}\n.highlight pre {\n  padding: 0;\n  margin-top: 0;\n  margin-bottom: 0;\n  word-break: normal;\n  white-space: nowrap;\n  background-color: transparent;\n  border: 0;\n}\n.highlight pre code {\n  font-size: inherit;\n  color: #333;\n}\n.highlight pre code:first-child {\n  display: inline-block;\n  padding-right: 45px;\n}\n.language-bash:before {\n  color: #033;\n  content: \"$ \";\n  user-select: none;\n}\n/*!\n * IE10 viewport hack for Surface/desktop Windows 8 bug\n * Copyright 2014-2019 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n */\n@-ms-viewport {\n  width: device-width;\n}\n@-o-viewport {\n  width: device-width;\n}\n@viewport {\n  width: device-width;\n}\n.btn-outline {\n  color: #563d7c;\n  background-color: transparent;\n  border-color: #563d7c;\n}\n.btn-outline:hover,\n.btn-outline:focus,\n.btn-outline:active {\n  color: #fff;\n  background-color: #563d7c;\n  border-color: #563d7c;\n}\n.btn-outline-inverse {\n  color: #fff;\n  background-color: transparent;\n  border-color: #cdbfe3;\n}\n.btn-outline-inverse:hover,\n.btn-outline-inverse:focus,\n.btn-outline-inverse:active {\n  color: #563d7c;\n  text-shadow: none;\n  background-color: #fff;\n  border-color: #fff;\n}\n.bs-docs-booticon {\n  display: block;\n  font-weight: 500;\n  color: #fff;\n  text-align: center;\n  cursor: default;\n  background-color: #563d7c;\n  border-radius: 15%;\n}\n.bs-docs-booticon-sm {\n  width: 30px;\n  height: 30px;\n  font-size: 20px;\n  line-height: 28px;\n}\n.bs-docs-booticon-lg {\n  width: 144px;\n  height: 144px;\n  font-size: 108px;\n  line-height: 140px;\n}\n.bs-docs-booticon-inverse {\n  color: #563d7c;\n  background-color: #fff;\n}\n.bs-docs-booticon-outline {\n  background-color: transparent;\n  border: 1px solid #cdbfe3;\n}\n#skippy {\n  display: block;\n  padding: 1em;\n  color: #fff;\n  background-color: #6f5499;\n  outline: 0;\n}\n#skippy .skiplink-text {\n  padding: 0.5em;\n  outline: 1px dotted;\n}\n#content:focus {\n  outline: none;\n}\n.bs-docs-nav {\n  margin-bottom: 0;\n  background-color: #fff;\n  border-bottom: 0;\n}\n.bs-docs-nav .bs-nav-b {\n  display: none;\n}\n.bs-docs-nav .navbar-brand,\n.bs-docs-nav .navbar-nav > li > a {\n  font-weight: 500;\n  color: #563d7c;\n}\n.bs-docs-nav .navbar-nav > li > a {\n  padding-right: 10px;\n  padding-left: 10px;\n}\n.bs-docs-nav .navbar-nav > li > a:hover,\n.bs-docs-nav .navbar-nav > .active > a,\n.bs-docs-nav .navbar-nav > .active > a:hover {\n  color: #463265;\n  background-color: #f9f9f9;\n}\n.bs-docs-nav .navbar-toggle .icon-bar {\n  background-color: #563d7c;\n}\n.bs-docs-nav .navbar-header .navbar-toggle {\n  border-color: #fff;\n}\n.bs-docs-nav .navbar-header .navbar-toggle:hover,\n.bs-docs-nav .navbar-header .navbar-toggle:focus {\n  background-color: #f9f9f9;\n  border-color: #f9f9f9;\n}\n@media (min-width: 768px) and (max-width: 992px) {\n  .bs-docs-nav .navbar-right {\n    display: none;\n  }\n}\n.bs-docs-footer {\n  padding-top: 50px;\n  padding-bottom: 50px;\n  margin-top: 100px;\n  color: #99979c;\n  text-align: center;\n  background-color: #2a2730;\n}\n.bs-docs-footer a {\n  color: #fff;\n}\n.bs-docs-footer-links {\n  padding-left: 0;\n  margin-bottom: 20px;\n}\n.bs-docs-footer-links li {\n  display: inline-block;\n}\n.bs-docs-footer-links li + li {\n  margin-left: 15px;\n}\n@media (min-width: 768px) {\n  .bs-docs-footer {\n    text-align: left;\n  }\n  .bs-docs-footer p {\n    margin-bottom: 0;\n  }\n}\n.bs-docs-masthead,\n.bs-docs-header {\n  position: relative;\n  padding: 30px 0;\n  color: #cdbfe3;\n  text-align: center;\n  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);\n  background-color: #6f5499;\n  background-image: -webkit-gradient(linear, left top, left bottom, from(#563d7c), to(#6f5499));\n  background-image: -webkit-linear-gradient(top, #563d7c 0%, #6f5499 100%);\n  background-image: -o-linear-gradient(top, #563d7c 0%, #6f5499 100%);\n  background-image: linear-gradient(to bottom, #563d7c 0%, #6f5499 100%);\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=\"#563d7c\", endColorstr=\"#6F5499\", GradientType=0);\n  background-repeat: repeat-x;\n}\n.bs-docs-masthead .bs-docs-booticon {\n  margin: 0 auto 30px;\n}\n.bs-docs-masthead h1 {\n  font-weight: 300;\n  line-height: 1;\n  color: #fff;\n}\n.bs-docs-masthead .lead {\n  margin: 0 auto 30px;\n  font-size: 20px;\n  color: #fff;\n}\n.bs-docs-masthead .version {\n  margin-top: -15px;\n  margin-bottom: 30px;\n  color: #9783b9;\n}\n.bs-docs-masthead .btn {\n  width: 100%;\n  padding: 15px 30px;\n  font-size: 20px;\n}\n@media (min-width: 480px) {\n  .bs-docs-masthead .btn {\n    width: auto;\n  }\n}\n@media (min-width: 768px) {\n  .bs-docs-masthead {\n    padding: 80px 0;\n  }\n  .bs-docs-masthead h1 {\n    font-size: 60px;\n  }\n  .bs-docs-masthead .lead {\n    font-size: 24px;\n  }\n}\n@media (min-width: 992px) {\n  .bs-docs-masthead .lead {\n    width: 80%;\n    font-size: 30px;\n  }\n}\n.bs-docs-header {\n  margin-bottom: 40px;\n  font-size: 20px;\n}\n.bs-docs-header h1 {\n  margin-top: 0;\n  color: #fff;\n}\n.bs-docs-header p {\n  margin-bottom: 0;\n  font-weight: 300;\n  line-height: 1.4;\n}\n.bs-docs-header .container {\n  position: relative;\n}\n@media (min-width: 768px) {\n  .bs-docs-header {\n    padding-top: 60px;\n    padding-bottom: 60px;\n    font-size: 24px;\n    text-align: left;\n  }\n  .bs-docs-header h1 {\n    font-size: 60px;\n    line-height: 1;\n  }\n}\n@media (min-width: 992px) {\n  .bs-docs-header h1,\n  .bs-docs-header p {\n    margin-right: 380px;\n  }\n}\n#carbonads {\n  display: block;\n  padding: 15px 15px 15px 160px;\n  margin: 50px -15px -30px;\n  overflow: hidden;\n  font-size: 13px;\n  line-height: 1.5;\n  text-align: left;\n  border: solid #866ab3;\n  border-width: 1px 0 0;\n}\n#carbonads a {\n  color: #fff;\n  text-decoration: none;\n}\n@media (min-width: 768px) {\n  #carbonads {\n    max-width: 330px;\n    margin: 50px auto 0;\n    border-width: 1px;\n    border-radius: 4px;\n  }\n}\n@media (min-width: 992px) {\n  #carbonads {\n    position: absolute;\n    top: 0;\n    right: 15px;\n    margin-top: 0;\n  }\n  .bs-docs-masthead #carbonads {\n    position: static;\n  }\n}\n.carbon-img {\n  float: left;\n  margin-left: -145px;\n}\n.carbon-poweredby {\n  display: block;\n  color: #cdbfe3 !important;\n}\n.bs-docs-featurette {\n  padding-top: 40px;\n  padding-bottom: 40px;\n  font-size: 16px;\n  line-height: 1.5;\n  color: #555;\n  text-align: center;\n  background-color: #fff;\n  border-bottom: 1px solid #e5e5e5;\n}\n.bs-docs-featurette + .bs-docs-footer {\n  margin-top: 0;\n  border-top: 0;\n}\n.bs-docs-featurette-title {\n  margin-bottom: 5px;\n  font-size: 30px;\n  font-weight: 400;\n  color: #333;\n}\n.half-rule {\n  width: 100px;\n  margin: 40px auto;\n}\n.bs-docs-featurette h3 {\n  margin-bottom: 5px;\n  font-weight: 400;\n  color: #333;\n}\n.bs-docs-featurette-img {\n  display: block;\n  margin-bottom: 20px;\n  color: #333;\n}\n.bs-docs-featurette-img:hover {\n  color: #337ab7;\n  text-decoration: none;\n}\n.bs-docs-featurette-img img {\n  display: block;\n  margin-bottom: 15px;\n}\n@media (min-width: 480px) {\n  .bs-docs-featurette .img-responsive {\n    margin-top: 30px;\n  }\n}\n@media (min-width: 768px) {\n  .bs-docs-featurette {\n    padding-top: 100px;\n    padding-bottom: 100px;\n  }\n  .bs-docs-featurette-title {\n    font-size: 40px;\n  }\n  .bs-docs-featurette .lead {\n    max-width: 80%;\n    margin-right: auto;\n    margin-left: auto;\n  }\n  .bs-docs-featurette .img-responsive {\n    margin-top: 0;\n  }\n}\n.bs-docs-featured-sites {\n  margin-right: -1px;\n  margin-left: -1px;\n}\n.bs-docs-featured-sites .col-xs-8 {\n  padding: 1px;\n}\n.bs-docs-featured-sites .img-responsive {\n  margin-top: 0;\n}\n@media (min-width: 768px) {\n  .bs-docs-featured-sites .col-sm-4:first-child img {\n    border-top-left-radius: 4px;\n    border-bottom-left-radius: 4px;\n  }\n  .bs-docs-featured-sites .col-sm-4:last-child img {\n    border-top-right-radius: 4px;\n    border-bottom-right-radius: 4px;\n  }\n}\n.bs-examples .thumbnail {\n  margin-bottom: 10px;\n}\n.bs-examples h4 {\n  margin-bottom: 5px;\n}\n.bs-examples p {\n  margin-bottom: 20px;\n}\n@media (max-width: 480px) {\n  .bs-examples {\n    margin-right: -10px;\n    margin-left: -10px;\n  }\n  .bs-examples > [class^=\"col-\"] {\n    padding-right: 10px;\n    padding-left: 10px;\n  }\n}\n.bs-docs-sidebar.affix {\n  position: static;\n}\n@media (min-width: 768px) {\n  .bs-docs-sidebar {\n    padding-left: 20px;\n  }\n}\n.bs-docs-search {\n  margin-bottom: 20px;\n  margin-left: 20px;\n}\n.bs-docs-sidenav {\n  margin-top: 20px;\n  margin-bottom: 20px;\n}\n.bs-docs-sidebar .nav > li > a {\n  display: block;\n  padding: 4px 20px;\n  font-size: 13px;\n  font-weight: 500;\n  color: #767676;\n}\n.bs-docs-sidebar .nav > li > a:hover,\n.bs-docs-sidebar .nav > li > a:focus {\n  padding-left: 19px;\n  color: #563d7c;\n  text-decoration: none;\n  background-color: transparent;\n  border-left: 1px solid #563d7c;\n}\n.bs-docs-sidebar .nav > .active > a,\n.bs-docs-sidebar .nav > .active:hover > a,\n.bs-docs-sidebar .nav > .active:focus > a {\n  padding-left: 18px;\n  font-weight: 700;\n  color: #563d7c;\n  background-color: transparent;\n  border-left: 2px solid #563d7c;\n}\n.bs-docs-sidebar .nav .nav {\n  display: none;\n  padding-bottom: 10px;\n}\n.bs-docs-sidebar .nav .nav > li > a {\n  padding-top: 1px;\n  padding-bottom: 1px;\n  padding-left: 30px;\n  font-size: 12px;\n  font-weight: 400;\n}\n.bs-docs-sidebar .nav .nav > li > a:hover,\n.bs-docs-sidebar .nav .nav > li > a:focus {\n  padding-left: 29px;\n}\n.bs-docs-sidebar .nav .nav > .active > a,\n.bs-docs-sidebar .nav .nav > .active:hover > a,\n.bs-docs-sidebar .nav .nav > .active:focus > a {\n  padding-left: 28px;\n  font-weight: 500;\n}\n.back-to-top,\n.bs-docs-theme-toggle {\n  display: none;\n  padding: 4px 10px;\n  margin-top: 10px;\n  margin-left: 10px;\n  font-size: 12px;\n  font-weight: 500;\n  color: #999;\n}\n.back-to-top:hover,\n.bs-docs-theme-toggle:hover {\n  color: #563d7c;\n  text-decoration: none;\n}\n.bs-docs-theme-toggle {\n  margin-top: 0;\n}\n@media (min-width: 768px) {\n  .back-to-top,\n  .bs-docs-theme-toggle {\n    display: block;\n  }\n}\n@media (min-width: 992px) {\n  .bs-docs-sidebar .nav > .active > ul {\n    display: block;\n  }\n  .bs-docs-sidebar.affix,\n  .bs-docs-sidebar.affix-bottom {\n    width: 213px;\n  }\n  .bs-docs-sidebar.affix {\n    position: fixed;\n    top: 20px;\n  }\n  .bs-docs-sidebar.affix-bottom {\n    position: absolute;\n  }\n  .bs-docs-sidebar.affix-bottom .bs-docs-sidenav,\n  .bs-docs-sidebar.affix .bs-docs-sidenav {\n    margin-top: 0;\n    margin-bottom: 0;\n  }\n}\n@media (min-width: 1200px) {\n  .bs-docs-sidebar.affix-bottom,\n  .bs-docs-sidebar.affix {\n    width: 263px;\n  }\n}\n.show-grid {\n  margin-bottom: 15px;\n}\n.show-grid [class^=\"col-\"] {\n  padding-top: 10px;\n  padding-bottom: 10px;\n  background-color: #eee;\n  background-color: rgba(86, 61, 124, 0.15);\n  border: 1px solid #ddd;\n  border: 1px solid rgba(86, 61, 124, 0.2);\n}\n.bs-example {\n  position: relative;\n  padding: 45px 15px 15px;\n  margin: 0 -15px 15px;\n  border-color: #e5e5e5 #eee #eee;\n  border-style: solid;\n  border-width: 1px 0;\n  box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.05);\n}\n.bs-example:after {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  font-size: 12px;\n  font-weight: 700;\n  color: #959595;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  content: \"Example\";\n}\n.bs-example-padded-bottom {\n  padding-bottom: 24px;\n}\n.bs-example + .highlight,\n.bs-example + .bs-clipboard + .highlight {\n  margin: -15px -15px 15px;\n  border-width: 0 0 1px;\n  border-radius: 0;\n}\n@media (min-width: 768px) {\n  .bs-example {\n    margin-right: 0;\n    margin-left: 0;\n    background-color: #fff;\n    border-color: #ddd;\n    border-width: 1px;\n    border-radius: 4px 4px 0 0;\n    box-shadow: none;\n  }\n  .bs-example + .highlight,\n  .bs-example + .bs-clipboard + .highlight {\n    margin-top: -16px;\n    margin-right: 0;\n    margin-left: 0;\n    border-width: 1px;\n    border-bottom-right-radius: 4px;\n    border-bottom-left-radius: 4px;\n  }\n  .bs-example + .bs-clipboard .btn-clipboard {\n    top: -15px;\n    border-top-right-radius: 0;\n  }\n  .bs-example-standalone {\n    border-radius: 4px;\n  }\n}\n.bs-example .container {\n  width: auto;\n}\n.bs-example > p:last-child,\n.bs-example > ul:last-child,\n.bs-example > ol:last-child,\n.bs-example > blockquote:last-child,\n.bs-example > .form-control:last-child,\n.bs-example > .table:last-child,\n.bs-example > .navbar:last-child,\n.bs-example > .jumbotron:last-child,\n.bs-example > .alert:last-child,\n.bs-example > .panel:last-child,\n.bs-example > .list-group:last-child,\n.bs-example > .well:last-child,\n.bs-example > .progress:last-child,\n.bs-example > .table-responsive:last-child > .table {\n  margin-bottom: 0;\n}\n.bs-example > p > .close {\n  float: none;\n}\n.bs-example-type .table .type-info {\n  color: #767676;\n  vertical-align: middle;\n}\n.bs-example-type .table td {\n  padding: 15px 0;\n  border-color: #eee;\n}\n.bs-example-type .table tr:first-child td {\n  border-top: 0;\n}\n.bs-example-type h1,\n.bs-example-type h2,\n.bs-example-type h3,\n.bs-example-type h4,\n.bs-example-type h5,\n.bs-example-type h6 {\n  margin: 0;\n}\n.bs-example-bg-classes p {\n  padding: 15px;\n}\n.bs-example > .img-circle,\n.bs-example > .img-rounded,\n.bs-example > .img-thumbnail {\n  margin: 5px;\n}\n.bs-example > .table-responsive > .table {\n  background-color: #fff;\n}\n.bs-example > .btn,\n.bs-example > .btn-group {\n  margin-top: 5px;\n  margin-bottom: 5px;\n}\n.bs-example > .btn-toolbar + .btn-toolbar {\n  margin-top: 10px;\n}\n.bs-example-control-sizing select,\n.bs-example-control-sizing input[type=\"text\"] + input[type=\"text\"] {\n  margin-top: 10px;\n}\n.bs-example-form .input-group {\n  margin-bottom: 10px;\n}\n.bs-example > textarea.form-control {\n  resize: vertical;\n}\n.bs-example > .list-group {\n  max-width: 400px;\n}\n.bs-example .navbar:last-child {\n  margin-bottom: 0;\n}\n.bs-navbar-top-example,\n.bs-navbar-bottom-example {\n  z-index: 1;\n  padding: 0;\n  overflow: hidden;\n}\n.bs-navbar-top-example .navbar-header,\n.bs-navbar-bottom-example .navbar-header {\n  margin-left: 0;\n}\n.bs-navbar-top-example .navbar-fixed-top,\n.bs-navbar-bottom-example .navbar-fixed-bottom {\n  position: relative;\n  margin-right: 0;\n  margin-left: 0;\n}\n.bs-navbar-top-example {\n  padding-bottom: 45px;\n}\n.bs-navbar-top-example:after {\n  top: auto;\n  bottom: 15px;\n}\n.bs-navbar-top-example .navbar-fixed-top {\n  top: -1px;\n}\n.bs-navbar-bottom-example {\n  padding-top: 45px;\n}\n.bs-navbar-bottom-example .navbar-fixed-bottom {\n  bottom: -1px;\n}\n.bs-navbar-bottom-example .navbar {\n  margin-bottom: 0;\n}\n@media (min-width: 768px) {\n  .bs-navbar-top-example .navbar-fixed-top,\n  .bs-navbar-bottom-example .navbar-fixed-bottom {\n    position: absolute;\n  }\n}\n.bs-example .pagination {\n  margin-top: 10px;\n  margin-bottom: 10px;\n}\n.bs-example > .pager {\n  margin-top: 0;\n}\n.bs-example-modal {\n  background-color: #f5f5f5;\n}\n.bs-example-modal .modal {\n  position: relative;\n  top: auto;\n  right: auto;\n  bottom: auto;\n  left: auto;\n  z-index: 1;\n  display: block;\n}\n.bs-example-modal .modal-dialog {\n  left: auto;\n  margin-right: auto;\n  margin-left: auto;\n}\n.bs-example > .dropdown > .dropdown-toggle {\n  float: left;\n}\n.bs-example > .dropdown > .dropdown-menu {\n  position: static;\n  display: block;\n  margin-bottom: 5px;\n  clear: left;\n}\n.bs-example-tabs .nav-tabs {\n  margin-bottom: 15px;\n}\n.bs-example-tooltips {\n  text-align: center;\n}\n.bs-example-tooltips > .btn {\n  margin-top: 5px;\n  margin-bottom: 5px;\n}\n.bs-example-tooltip .tooltip {\n  position: relative;\n  display: inline-block;\n  margin: 10px 20px;\n  opacity: 1;\n}\n.bs-example-popover {\n  padding-bottom: 24px;\n  background-color: #f9f9f9;\n}\n.bs-example-popover .popover {\n  position: relative;\n  display: block;\n  float: left;\n  width: 260px;\n  margin: 20px;\n}\n.scrollspy-example {\n  position: relative;\n  height: 200px;\n  margin-top: 10px;\n  overflow: auto;\n}\n.bs-example > .nav-pills-stacked-example {\n  max-width: 300px;\n}\n#collapseExample .well {\n  margin-bottom: 0;\n}\n#focusedInput {\n  border-color: #cccccc;\n  border-color: rgba(82, 168, 236, 0.8);\n  outline: 0;\n  outline: thin dotted \\9;\n  box-shadow: 0 0 8px rgba(82, 168, 236, 0.6);\n}\n.bs-callout {\n  padding: 20px;\n  margin: 20px 0;\n  border: 1px solid #eee;\n  border-left-width: 5px;\n  border-radius: 3px;\n}\n.bs-callout h4 {\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n.bs-callout p:last-child {\n  margin-bottom: 0;\n}\n.bs-callout code {\n  border-radius: 3px;\n}\n.bs-callout + .bs-callout {\n  margin-top: -5px;\n}\n.bs-callout-danger {\n  border-left-color: #ce4844;\n}\n.bs-callout-danger h4 {\n  color: #ce4844;\n}\n.bs-callout-warning {\n  border-left-color: #aa6708;\n}\n.bs-callout-warning h4 {\n  color: #aa6708;\n}\n.bs-callout-info {\n  border-left-color: #1b809e;\n}\n.bs-callout-info h4 {\n  color: #1b809e;\n}\n.color-swatches {\n  margin: 0 -5px;\n  overflow: hidden;\n  /* clearfix */\n}\n.color-swatch {\n  float: left;\n  width: 60px;\n  height: 60px;\n  margin: 0 5px;\n  border-radius: 3px;\n}\n@media (min-width: 768px) {\n  .color-swatch {\n    width: 100px;\n    height: 100px;\n  }\n}\n.color-swatches .gray-darker {\n  background-color: #222;\n}\n.color-swatches .gray-dark {\n  background-color: #333;\n}\n.color-swatches .gray {\n  background-color: #555;\n}\n.color-swatches .gray-light {\n  background-color: #999;\n}\n.color-swatches .gray-lighter {\n  background-color: #eee;\n}\n.color-swatches .brand-primary {\n  background-color: #337ab7;\n}\n.color-swatches .brand-success {\n  background-color: #5cb85c;\n}\n.color-swatches .brand-warning {\n  background-color: #f0ad4e;\n}\n.color-swatches .brand-danger {\n  background-color: #d9534f;\n}\n.color-swatches .brand-info {\n  background-color: #5bc0de;\n}\n.color-swatches .bs-purple {\n  background-color: #563d7c;\n}\n.color-swatches .bs-purple-light {\n  background-color: #c7bfd3;\n}\n.color-swatches .bs-purple-lighter {\n  background-color: #e5e1ea;\n}\n.color-swatches .bs-gray {\n  background-color: #f9f9f9;\n}\n.bs-team .team-member {\n  line-height: 32px;\n  color: #555;\n}\n.bs-team .team-member:hover {\n  color: #333;\n  text-decoration: none;\n}\n.bs-team .github-btn {\n  float: right;\n  width: 180px;\n  height: 20px;\n  margin-top: 6px;\n  border: none;\n}\n.bs-team img {\n  float: left;\n  width: 32px;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n.table-responsive .highlight pre {\n  white-space: normal;\n}\n.bs-table th small,\n.responsive-utilities th small {\n  display: block;\n  font-weight: 400;\n  color: #999;\n}\n.responsive-utilities tbody th {\n  font-weight: 400;\n}\n.responsive-utilities td {\n  text-align: center;\n}\n.responsive-utilities td.is-visible {\n  color: #468847;\n  background-color: #dff0d8 !important;\n}\n.responsive-utilities td.is-hidden {\n  color: #ccc;\n  background-color: #f9f9f9 !important;\n}\n.responsive-utilities-test {\n  margin-top: 5px;\n}\n.responsive-utilities-test .col-xs-6 {\n  margin-bottom: 10px;\n}\n.responsive-utilities-test span {\n  display: block;\n  padding: 15px 10px;\n  font-size: 14px;\n  font-weight: 700;\n  line-height: 1.1;\n  text-align: center;\n  border-radius: 4px;\n}\n.visible-on .col-xs-6 .hidden-xs,\n.visible-on .col-xs-6 .hidden-sm,\n.visible-on .col-xs-6 .hidden-md,\n.visible-on .col-xs-6 .hidden-lg,\n.hidden-on .col-xs-6 .hidden-xs,\n.hidden-on .col-xs-6 .hidden-sm,\n.hidden-on .col-xs-6 .hidden-md,\n.hidden-on .col-xs-6 .hidden-lg {\n  color: #999;\n  border: 1px solid #ddd;\n}\n.visible-on .col-xs-6 .visible-xs-block,\n.visible-on .col-xs-6 .visible-sm-block,\n.visible-on .col-xs-6 .visible-md-block,\n.visible-on .col-xs-6 .visible-lg-block,\n.hidden-on .col-xs-6 .visible-xs-block,\n.hidden-on .col-xs-6 .visible-sm-block,\n.hidden-on .col-xs-6 .visible-md-block,\n.hidden-on .col-xs-6 .visible-lg-block {\n  color: #468847;\n  background-color: #dff0d8;\n  border: 1px solid #d6e9c6;\n}\n.bs-glyphicons {\n  margin: 0 -10px 20px;\n  overflow: hidden;\n}\n.bs-glyphicons-list {\n  padding-left: 0;\n  list-style: none;\n}\n.bs-glyphicons li {\n  float: left;\n  width: 25%;\n  height: 115px;\n  padding: 10px;\n  font-size: 10px;\n  line-height: 1.4;\n  text-align: center;\n  background-color: #f9f9f9;\n  border: 1px solid #fff;\n}\n.bs-glyphicons .glyphicon {\n  margin-top: 5px;\n  margin-bottom: 10px;\n  font-size: 24px;\n}\n.bs-glyphicons .glyphicon-class {\n  display: block;\n  text-align: center;\n  word-wrap: break-word;\n}\n.bs-glyphicons li:hover {\n  color: #fff;\n  background-color: #563d7c;\n}\n@media (min-width: 768px) {\n  .bs-glyphicons {\n    margin-right: 0;\n    margin-left: 0;\n  }\n  .bs-glyphicons li {\n    width: 12.5%;\n    font-size: 12px;\n  }\n}\n.bs-customizer .toggle {\n  float: right;\n  margin-top: 25px;\n}\n.bs-customizer label {\n  margin-top: 10px;\n  font-weight: 500;\n  color: #555;\n}\n.bs-customizer h2 {\n  padding-top: 30px;\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n.bs-customizer h3 {\n  margin-bottom: 0;\n}\n.bs-customizer h4 {\n  margin-top: 15px;\n  margin-bottom: 0;\n}\n.bs-customizer .bs-callout h4 {\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n.bs-customizer input[type=\"text\"] {\n  font-family: Menlo, Monaco, Consolas, \"Courier New\", monospace;\n  background-color: #fafafa;\n}\n.bs-customizer .help-block {\n  margin-bottom: 5px;\n  font-size: 12px;\n}\n#less-section label {\n  font-weight: 400;\n}\n.bs-customize-download .btn-outline {\n  padding: 20px;\n}\n.bs-customizer-alert {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1030;\n  padding: 15px 0;\n  color: #fff;\n  background-color: #d9534f;\n  border-bottom: 1px solid #b94441;\n  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25);\n}\n.bs-customizer-alert .close {\n  margin-top: -4px;\n  font-size: 24px;\n}\n.bs-customizer-alert p {\n  margin-bottom: 0;\n}\n.bs-customizer-alert .glyphicon {\n  margin-right: 5px;\n}\n.bs-customizer-alert pre {\n  margin: 10px 0 0;\n  color: #fff;\n  background-color: #a83c3a;\n  border-color: #973634;\n  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 0 rgba(255, 255, 255, 0.1);\n}\n.bs-dropzone {\n  position: relative;\n  padding: 20px;\n  margin-bottom: 20px;\n  color: #777;\n  text-align: center;\n  border: 2px dashed #eee;\n  border-radius: 4px;\n}\n.bs-dropzone .import-header {\n  margin-bottom: 5px;\n}\n.bs-dropzone .glyphicon-folder-open {\n  font-size: 40px;\n}\n.bs-dropzone hr {\n  width: 100px;\n}\n.bs-dropzone .lead {\n  margin-bottom: 10px;\n  font-weight: 400;\n  color: #333;\n}\n#import-manual-trigger {\n  cursor: pointer;\n}\n.bs-dropzone p:last-child {\n  margin-bottom: 0;\n}\n.bs-brand-logos {\n  display: table;\n  width: 100%;\n  margin-bottom: 15px;\n  overflow: hidden;\n  color: #563d7c;\n  background-color: #f9f9f9;\n  border-radius: 4px;\n}\n.bs-brand-item {\n  padding: 60px 0;\n  text-align: center;\n}\n.bs-brand-item + .bs-brand-item {\n  border-top: 1px solid #fff;\n}\n.bs-brand-logos .inverse {\n  color: #fff;\n  background-color: #563d7c;\n}\n.bs-brand-item h1,\n.bs-brand-item h3 {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.bs-brand-item .bs-docs-booticon {\n  margin-right: auto;\n  margin-left: auto;\n}\n.bs-brand-item .glyphicon {\n  width: 30px;\n  height: 30px;\n  margin: 10px auto -10px;\n  line-height: 30px;\n  color: #fff;\n  border-radius: 50%;\n}\n.bs-brand-item .glyphicon-ok {\n  background-color: #5cb85c;\n}\n.bs-brand-item .glyphicon-remove {\n  background-color: #d9534f;\n}\n@media (min-width: 768px) {\n  .bs-brand-item {\n    display: table-cell;\n    width: 1%;\n  }\n  .bs-brand-item + .bs-brand-item {\n    border-top: 0;\n    border-left: 1px solid #fff;\n  }\n  .bs-brand-item h1 {\n    font-size: 60px;\n  }\n}\n.bs-clipboard {\n  position: relative;\n  display: none;\n  float: right;\n}\n.bs-clipboard + .highlight {\n  margin-top: 0;\n}\n.btn-clipboard {\n  position: absolute;\n  top: 0;\n  right: 0;\n  z-index: 10;\n  display: block;\n  padding: 4px 8px;\n  font-size: 12px;\n  color: #818a91;\n  cursor: pointer;\n  background-color: transparent;\n  border: 0;\n  border-top-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n.btn-clipboard:hover {\n  color: #fff;\n  background-color: #027de7;\n}\n@media (min-width: 768px) {\n  .bs-clipboard {\n    display: block;\n  }\n}\n.anchorjs-link {\n  color: inherit;\n}\n@media (max-width: 480px) {\n  .anchorjs-link {\n    display: none;\n  }\n}\n*:hover > .anchorjs-link {\n  opacity: 0.75;\n  transition: color 0.16s linear;\n}\n*:hover > .anchorjs-link:hover,\n.anchorjs-link:focus {\n  text-decoration: none;\n  opacity: 1;\n}\n.algolia-autocomplete {\n  display: block !important;\n}\n.algolia-autocomplete .ds-dropdown-menu {\n  width: 100%;\n  min-width: 0 !important;\n  max-width: none !important;\n  padding: 10px 0 !important;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid #ddd;\n  border: 1px solid rgba(0, 0, 0, 0.1);\n  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.175);\n}\n@media (min-width: 768px) {\n  .algolia-autocomplete .ds-dropdown-menu {\n    width: 175%;\n  }\n}\n.algolia-autocomplete .ds-dropdown-menu:before {\n  display: none !important;\n}\n.algolia-autocomplete .ds-dropdown-menu [class^=\"ds-dataset-\"] {\n  padding: 0 !important;\n  overflow: visible !important;\n  background-color: transparent !important;\n  border: 0 !important;\n}\n.algolia-autocomplete .ds-dropdown-menu .ds-suggestions {\n  margin-top: 0 !important;\n}\n.algolia-autocomplete .ds-dropdown-menu .ds-input {\n  box-shadow: none;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion {\n  padding: 0 !important;\n  overflow: visible !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--category-header {\n  padding: 2px 15px !important;\n  margin-top: 0 !important;\n  font-size: 13px !important;\n  font-weight: 500 !important;\n  color: #7952b3 !important;\n  border-bottom: 0 !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--wrapper {\n  float: none !important;\n  padding-top: 0 !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--subcategory-column {\n  float: none !important;\n  width: auto !important;\n  padding: 0 !important;\n  text-align: left !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--content {\n  float: none !important;\n  width: auto !important;\n  padding: 0 !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--content:before {\n  display: none !important;\n}\n.algolia-autocomplete .ds-suggestion:not(:first-child) .algolia-docsearch-suggestion--category-header {\n  padding-top: 10px !important;\n  margin-top: 10px !important;\n  border-top: 1px solid #eee;\n}\n.algolia-autocomplete .ds-suggestion .algolia-docsearch-suggestion--subcategory-column {\n  display: none !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--title {\n  display: block;\n  padding: 4px 15px !important;\n  margin-bottom: 0 !important;\n  font-size: 13px !important;\n  font-weight: 400 !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--text {\n  padding: 0 15px 8px !important;\n  margin-top: -4px;\n  font-size: 13px !important;\n  font-weight: 400;\n  line-height: 1.25 !important;\n}\n.algolia-autocomplete .algolia-docsearch-footer {\n  float: none !important;\n  width: auto !important;\n  height: auto !important;\n  padding: 10px 15px 0;\n  font-size: 10px !important;\n  line-height: 1 !important;\n  color: #767676 !important;\n  border-top: 1px solid #eee;\n}\n.algolia-autocomplete .algolia-docsearch-footer--logo {\n  display: inline !important;\n  overflow: visible !important;\n  color: inherit !important;\n  text-indent: 0 !important;\n  background: none !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--highlight {\n  color: #5f2dab;\n  background-color: #eee;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--text .algolia-docsearch-suggestion--highlight {\n  box-shadow: inset 0 -2px 0 0 rgba(95, 45, 171, 0.5) !important;\n}\n.algolia-autocomplete .ds-suggestion.ds-cursor .algolia-docsearch-suggestion--content {\n  background-color: #e5e5e5 !important;\n}\nbody {\n  position: relative;\n}\n.table code {\n  font-size: 13px;\n  font-weight: 400;\n}\nh2 code,\nh3 code,\nh4 code {\n  background-color: inherit;\n}\n.bs-docs-section {\n  margin-bottom: 60px;\n}\n.bs-docs-section:last-child {\n  margin-bottom: 0;\n}\nh1[id] {\n  padding-top: 20px;\n  margin-top: 0;\n}\n.bs-docs-browser-bugs td p {\n  margin-bottom: 0;\n}\n.bs-docs-browser-bugs th:first-child {\n  width: 18%;\n}\n.bs-events-table > thead > tr > th:first-child,\n.bs-events-table > tbody > tr > td:first-child {\n  white-space: nowrap;\n}\n.bs-events-table > thead > tr > th:first-child {\n  width: 150px;\n}\n.js-options-table > thead > tr > th:nth-child(1),\n.js-options-table > thead > tr > th:nth-child(2) {\n  width: 100px;\n}\n.js-options-table > thead > tr > th:nth-child(3) {\n  width: 50px;\n}\n.v4-tease {\n  display: block;\n  padding: 15px 20px;\n  font-weight: 700;\n  color: #fff;\n  text-align: center;\n  background-color: #0275d8;\n}\n.v4-tease:focus,\n.v4-tease:hover {\n  color: #fff;\n  text-decoration: none;\n  background-color: #0269c2;\n}\n/* Nullify ill-advised printing of hrefs; see #18711 */\n@media print {\n  a[href]:after {\n    content: \"\" !important;\n  }\n}\n/*# sourceMappingURL=docs.css.map */", "// stylelint-disable declaration-block-single-line-max-declarations\n\n.hll { background-color: #ffc; }\n.c { color: #999; }\n.err { color: #a00; background-color: #faa; }\n.k { color: #069; }\n.o { color: #555; }\n.cm { color: #999; }\n.cp { color: #099; }\n.c1 { color: #999; }\n.cs { color: #999; }\n.gd { background-color: #fcc; border: 1px solid #c00; }\n.ge { font-style: italic; }\n.gr { color: #f00; }\n.gh { color: #030; }\n.gi { background-color: #cfc; border: 1px solid #0c0; }\n.go { color: #aaa; }\n.gp { color: #009; }\n.gu { color: #030; }\n.gt { color: #9c6; }\n.kc { color: #069; }\n.kd { color: #069; }\n.kn { color: #069; }\n.kp { color: #069; }\n.kr { color: #069; }\n.kt { color: #078; }\n.m { color: #f60; }\n.s { color: #d44950; }\n.na { color: #4f9fcf; }\n.nb { color: #366; }\n.nc { color: #0a8; }\n.no { color: #360; }\n.nd { color: #99f; }\n.ni { color: #999; }\n.ne { color: #c00; }\n.nf { color: #c0f; }\n.nl { color: #99f; }\n.nn { color: #0cf; }\n.nt { color: #2f6f9f; }\n.nv { color: #033; }\n.ow { color: #000; }\n.w { color: #bbb; }\n.mf { color: #f60; }\n.mh { color: #f60; }\n.mi { color: #f60; }\n.mo { color: #f60; }\n.sb { color: #c30; }\n.sc { color: #c30; }\n.sd { font-style: italic; color: #c30; }\n.s2 { color: #c30; }\n.se { color: #c30; }\n.sh { color: #c30; }\n.si { color: #a00; }\n.sx { color: #c30; }\n.sr { color: #3aa; }\n.s1 { color: #c30; }\n.ss { color: #fc3; }\n.bp { color: #366; }\n.vc { color: #033; }\n.vg { color: #033; }\n.vi { color: #033; }\n.il { color: #f60; }\n\n.css .o,\n.css .o + .nt,\n.css .nt + .nt { color: #999; }\n\n\n//\n// Docs additions\n//\n\n.highlight {\n  padding: 9px 14px;\n  margin-bottom: 14px;\n  background-color: #f7f7f9;\n  border: 1px solid #e1e1e8;\n  border-radius: 4px;\n}\n.highlight pre {\n  padding: 0;\n  margin-top: 0;\n  margin-bottom: 0;\n  word-break: normal;\n  white-space: nowrap;\n  background-color: transparent;\n  border: 0;\n}\n.highlight pre code {\n  font-size: inherit;\n  color: #333; // Effectively the base text color\n}\n.highlight pre code:first-child {\n  display: inline-block;\n  padding-right: 45px;\n}\n\n.language-bash:before {\n  color: #033;\n  content: \"$ \";\n  user-select: none;\n}\n", "// stylelint-disable at-rule-no-vendor-prefix\n\n/*!\n * IE10 viewport hack for Surface/desktop Windows 8 bug\n * Copyright 2014-2019 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n */\n\n// See the Getting Started docs for more information:\n// https://getbootstrap.com/docs/3.4/getting-started/#support-ie10-width\n\n@-ms-viewport     { width: device-width; }\n@-o-viewport      { width: device-width; }\n@viewport         { width: device-width; }\n", "// Outline button for use within the docs\n.btn-outline {\n  color: #563d7c;\n  background-color: transparent;\n  border-color: #563d7c;\n\n  &:hover,\n  &:focus,\n  &:active {\n    color: #fff;\n    background-color: #563d7c;\n    border-color: #563d7c;\n  }\n}\n\n// Inverted outline button (white on dark)\n.btn-outline-inverse {\n  color: #fff;\n  background-color: transparent;\n  border-color: #cdbfe3;\n\n  &:hover,\n  &:focus,\n  &:active {\n    color: #563d7c;\n    text-shadow: none;\n    background-color: #fff;\n    border-color: #fff;\n  }\n}\n", ".bs-docs-booticon {\n  display: block;\n  font-weight: 500;\n  color: #fff;\n  text-align: center;\n  cursor: default;\n  background-color: #563d7c;\n  border-radius: 15%;\n}\n\n.bs-docs-booticon-sm {\n  width: 30px;\n  height: 30px;\n  font-size: 20px;\n  line-height: 28px;\n}\n\n.bs-docs-booticon-lg {\n  width: 144px;\n  height: 144px;\n  font-size: 108px;\n  line-height: 140px;\n}\n\n.bs-docs-booticon-inverse {\n  color: #563d7c;\n  background-color: #fff;\n}\n\n.bs-docs-booticon-outline {\n  background-color: transparent;\n  border: 1px solid #cdbfe3;\n}\n", "// stylelint-disable selector-max-id\n\n#skippy {\n  display: block;\n  padding: 1em;\n  color: #fff;\n  background-color: #6f5499;\n  outline: 0;\n}\n\n#skippy .skiplink-text {\n  padding: .5em;\n  outline: 1px dotted;\n}\n\n#content:focus {\n  outline: none;\n}\n", ".bs-docs-nav {\n  margin-bottom: 0;\n  background-color: #fff;\n  border-bottom: 0;\n\n  .bs-nav-b {\n    display: none;\n  }\n\n  .navbar-brand,\n  .navbar-nav > li > a {\n    font-weight: 500;\n    color: #563d7c;\n  }\n\n  .navbar-nav {\n    > li > a {\n      padding-right: 10px;\n      padding-left: 10px;\n    }\n\n    > li > a:hover,\n    > .active > a,\n    > .active > a:hover {\n      color: #463265;\n      background-color: #f9f9f9;\n    }\n  }\n\n  .navbar-toggle .icon-bar {\n    background-color: #563d7c;\n  }\n\n  .navbar-header {\n    .navbar-toggle {\n      border-color: #fff;\n\n      &:hover,\n      &:focus {\n        background-color: #f9f9f9;\n        border-color: #f9f9f9;\n      }\n    }\n  }\n\n  .navbar-right {\n    @media (min-width: 768px) and (max-width: 992px) {\n      display: none;\n    }\n  }\n}\n", ".bs-docs-footer {\n  padding-top: 50px;\n  padding-bottom: 50px;\n  margin-top: 100px;\n  color: #99979c;\n  text-align: center;\n  background-color: #2a2730;\n}\n.bs-docs-footer a {\n  color: #fff;\n}\n.bs-docs-footer-links {\n  padding-left: 0;\n  margin-bottom: 20px;\n}\n.bs-docs-footer-links li {\n  display: inline-block;\n}\n.bs-docs-footer-links li + li {\n  margin-left: 15px;\n}\n\n@media (min-width: @screen-sm-min) {\n  .bs-docs-footer {\n    text-align: left;\n  }\n  .bs-docs-footer p {\n    margin-bottom: 0;\n  }\n}\n", "// stylelint-disable value-no-vendor-prefix, function-name-case\n\n.bs-docs-masthead,\n.bs-docs-header {\n  position: relative;\n  padding: 30px 0;\n  color: #cdbfe3;\n  text-align: center;\n  text-shadow: 0 1px 0 rgba(0, 0, 0, .1);\n  background-color: #6f5499;\n  background-image: -webkit-gradient(linear, left top, left bottom, from(#563d7c), to(#6f5499));\n  background-image: -webkit-linear-gradient(top, #563d7c 0%, #6f5499 100%);\n  background-image: -o-linear-gradient(top, #563d7c 0%, #6f5499 100%);\n  background-image: linear-gradient(to bottom, #563d7c 0%, #6f5499 100%);\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=\"#563d7c\", endColorstr=\"#6F5499\", GradientType=0);\n  background-repeat: repeat-x;\n}\n\n// Masthead (headings and download button)\n.bs-docs-masthead .bs-docs-booticon {\n  margin: 0 auto 30px;\n}\n.bs-docs-masthead h1 {\n  font-weight: 300;\n  line-height: 1;\n  color: #fff;\n}\n.bs-docs-masthead .lead {\n  margin: 0 auto 30px;\n  font-size: 20px;\n  color: #fff;\n}\n.bs-docs-masthead .version {\n  margin-top: -15px;\n  margin-bottom: 30px;\n  color: #9783b9;\n}\n.bs-docs-masthead .btn {\n  width: 100%;\n  padding: 15px 30px;\n  font-size: 20px;\n}\n\n@media (min-width: @screen-xs-min) {\n  .bs-docs-masthead .btn {\n    width: auto;\n  }\n}\n\n@media (min-width: @screen-sm-min) {\n  .bs-docs-masthead {\n    padding: 80px 0;\n  }\n  .bs-docs-masthead h1 {\n    font-size: 60px;\n  }\n  .bs-docs-masthead .lead {\n    font-size: 24px;\n  }\n}\n\n@media (min-width: @screen-md-min) {\n  .bs-docs-masthead .lead {\n    width: 80%;\n    font-size: 30px;\n  }\n}\n", ".bs-docs-header {\n  margin-bottom: 40px;\n  font-size: 20px;\n}\n.bs-docs-header h1 {\n  margin-top: 0;\n  color: #fff;\n}\n.bs-docs-header p {\n  margin-bottom: 0;\n  font-weight: 300;\n  line-height: 1.4;\n}\n.bs-docs-header .container {\n  position: relative;\n}\n\n@media (min-width: @screen-sm-min) {\n  .bs-docs-header {\n    padding-top: 60px;\n    padding-bottom: 60px;\n    font-size: 24px;\n    text-align: left;\n  }\n  .bs-docs-header h1 {\n    font-size: 60px;\n    line-height: 1;\n  }\n}\n\n@media (min-width: @screen-md-min) {\n  .bs-docs-header h1,\n  .bs-docs-header p {\n    margin-right: 380px;\n  }\n}\n", "// stylelint-disable selector-max-id, declaration-no-important\n\n#carbonads {\n  display: block;\n  padding: 15px 15px 15px 160px;\n  margin: 50px -15px -30px;\n  overflow: hidden;\n  font-size: 13px;\n  line-height: 1.5;\n  text-align: left;\n  border: solid #866ab3;\n  border-width: 1px 0 0;\n\n  a {\n    color: #fff;\n    text-decoration: none;\n  }\n\n  @media (min-width: @screen-sm-min) {\n    max-width: 330px;\n    margin: 50px auto 0;\n    border-width: 1px;\n    border-radius: 4px;\n  }\n\n  @media (min-width: @screen-md-min) {\n    position: absolute;\n    top: 0;\n    right: 15px;\n    margin-top: 0;\n\n    .bs-docs-masthead & {\n      position: static;\n    }\n  }\n}\n\n.carbon-img {\n  float: left;\n  margin-left: -145px;\n}\n\n.carbon-poweredby {\n  display: block;\n  color: #cdbfe3 !important;\n}\n", ".bs-docs-featurette {\n  padding-top: 40px;\n  padding-bottom: 40px;\n  font-size: 16px;\n  line-height: 1.5;\n  color: #555;\n  text-align: center;\n  background-color: #fff;\n  border-bottom: 1px solid #e5e5e5;\n}\n.bs-docs-featurette + .bs-docs-footer {\n  margin-top: 0;\n  border-top: 0;\n}\n\n.bs-docs-featurette-title {\n  margin-bottom: 5px;\n  font-size: 30px;\n  font-weight: 400;\n  color: #333;\n}\n.half-rule {\n  width: 100px;\n  margin: 40px auto;\n}\n.bs-docs-featurette h3 {\n  margin-bottom: 5px;\n  font-weight: 400;\n  color: #333;\n}\n.bs-docs-featurette-img {\n  display: block;\n  margin-bottom: 20px;\n  color: #333;\n}\n.bs-docs-featurette-img:hover {\n  color: #337ab7;\n  text-decoration: none;\n}\n.bs-docs-featurette-img img {\n  display: block;\n  margin-bottom: 15px;\n}\n\n@media (min-width: @screen-xs-min) {\n  .bs-docs-featurette .img-responsive {\n    margin-top: 30px;\n  }\n}\n@media (min-width: @screen-sm-min) {\n  .bs-docs-featurette {\n    padding-top: 100px;\n    padding-bottom: 100px;\n  }\n  .bs-docs-featurette-title {\n    font-size: 40px;\n  }\n  .bs-docs-featurette .lead {\n    max-width: 80%;\n    margin-right: auto;\n    margin-left: auto;\n  }\n  .bs-docs-featurette .img-responsive {\n    margin-top: 0;\n  }\n}\n", ".bs-docs-featured-sites {\n  margin-right: -1px;\n  margin-left: -1px;\n}\n.bs-docs-featured-sites .col-xs-8 {\n  padding: 1px;\n}\n.bs-docs-featured-sites .img-responsive {\n  margin-top: 0;\n}\n\n@media (min-width: 768px) {\n  .bs-docs-featured-sites .col-sm-4:first-child img {\n    border-top-left-radius: 4px;\n    border-bottom-left-radius: 4px;\n  }\n  .bs-docs-featured-sites .col-sm-4:last-child img {\n    border-top-right-radius: 4px;\n    border-bottom-right-radius: 4px;\n  }\n}\n", ".bs-examples {\n  .thumbnail {\n    margin-bottom: 10px;\n  }\n\n  h4 { margin-bottom: 5px; }\n\n  p { margin-bottom: 20px; }\n\n  @media (max-width: @screen-xs-min) {\n    margin-right: -10px;\n    margin-left: -10px;\n\n    > [class^=\"col-\"] {\n      padding-right: 10px;\n      padding-left: 10px;\n    }\n  }\n}\n", "// stylelint-disable selector-max-compound-selectors\n\n// By default it's not affixed in mobile views, so undo that\n.bs-docs-sidebar.affix {\n  position: static;\n}\n@media (min-width: @screen-sm-min) {\n  .bs-docs-sidebar {\n    padding-left: 20px;\n  }\n}\n\n.bs-docs-search {\n  margin-bottom: 20px;\n  margin-left: 20px;\n}\n\n// First level of nav\n.bs-docs-sidenav {\n  margin-top: 20px;\n  margin-bottom: 20px;\n}\n\n// All levels of nav\n.bs-docs-sidebar .nav > li > a {\n  display: block;\n  padding: 4px 20px;\n  font-size: 13px;\n  font-weight: 500;\n  color: #767676;\n}\n.bs-docs-sidebar .nav > li > a:hover,\n.bs-docs-sidebar .nav > li > a:focus {\n  padding-left: 19px;\n  color: #563d7c;\n  text-decoration: none;\n  background-color: transparent;\n  border-left: 1px solid #563d7c;\n}\n.bs-docs-sidebar .nav > .active > a,\n.bs-docs-sidebar .nav > .active:hover > a,\n.bs-docs-sidebar .nav > .active:focus > a {\n  padding-left: 18px;\n  font-weight: 700;\n  color: #563d7c;\n  background-color: transparent;\n  border-left: 2px solid #563d7c;\n}\n\n// Nav: second level (shown on .active)\n.bs-docs-sidebar .nav .nav {\n  display: none; // Hide by default, but at >768px, show it\n  padding-bottom: 10px;\n}\n.bs-docs-sidebar .nav .nav > li > a {\n  padding-top: 1px;\n  padding-bottom: 1px;\n  padding-left: 30px;\n  font-size: 12px;\n  font-weight: 400;\n}\n.bs-docs-sidebar .nav .nav > li > a:hover,\n.bs-docs-sidebar .nav .nav > li > a:focus {\n  padding-left: 29px;\n}\n.bs-docs-sidebar .nav .nav > .active > a,\n.bs-docs-sidebar .nav .nav > .active:hover > a,\n.bs-docs-sidebar .nav .nav > .active:focus > a {\n  padding-left: 28px;\n  font-weight: 500;\n}\n\n// Back to top (hidden on mobile)\n.back-to-top,\n.bs-docs-theme-toggle {\n  display: none;\n  padding: 4px 10px;\n  margin-top: 10px;\n  margin-left: 10px;\n  font-size: 12px;\n  font-weight: 500;\n  color: #999;\n}\n.back-to-top:hover,\n.bs-docs-theme-toggle:hover {\n  color: #563d7c;\n  text-decoration: none;\n}\n.bs-docs-theme-toggle {\n  margin-top: 0;\n}\n\n@media (min-width: @screen-sm-min) {\n  .back-to-top,\n  .bs-docs-theme-toggle {\n    display: block;\n  }\n}\n\n// Show and affix the side nav when space allows it\n@media (min-width: @screen-md-min) {\n  .bs-docs-sidebar .nav > .active > ul {\n    display: block;\n  }\n  // Widen the fixed sidebar\n  .bs-docs-sidebar.affix,\n  .bs-docs-sidebar.affix-bottom {\n    width: 213px;\n  }\n  .bs-docs-sidebar.affix {\n    position: fixed; // Undo the static from mobile first approach\n    top: 20px;\n  }\n  .bs-docs-sidebar.affix-bottom {\n    position: absolute; // Undo the static from mobile first approach\n  }\n  .bs-docs-sidebar.affix-bottom .bs-docs-sidenav,\n  .bs-docs-sidebar.affix .bs-docs-sidenav {\n    margin-top: 0;\n    margin-bottom: 0;\n  }\n}\n@media (min-width: @screen-lg-min) {\n  // Widen the fixed sidebar again\n  .bs-docs-sidebar.affix-bottom,\n  .bs-docs-sidebar.affix {\n    width: 263px;\n  }\n}\n", "// stylelint-disable selector-max-id, selector-no-qualifying-type\n\n// Grid examples\n//\n// Highlight the grid columns within the docs so folks can see their padding,\n// alignment, sizing, etc.\n\n.show-grid {\n  margin-bottom: 15px;\n}\n.show-grid [class^=\"col-\"] {\n  padding-top: 10px;\n  padding-bottom: 10px;\n  background-color: #eee;\n  background-color: rgba(86, 61, 124, .15);\n  border: 1px solid #ddd;\n  border: 1px solid rgba(86, 61, 124, .2);\n}\n\n// Examples\n//\n// Isolated sections of example content for each component or feature. Usually\n// followed by a code snippet.\n\n.bs-example {\n  position: relative;\n  padding: 45px 15px 15px;\n  margin: 0 -15px 15px;\n  border-color: #e5e5e5 #eee #eee;\n  border-style: solid;\n  border-width: 1px 0;\n  box-shadow: inset 0 3px 6px rgba(0, 0, 0, .05);\n}\n// Echo out a label for the example\n.bs-example:after {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  font-size: 12px;\n  font-weight: 700;\n  color: #959595;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  content: \"Example\";\n}\n\n.bs-example-padded-bottom {\n  padding-bottom: 24px;\n}\n\n// Tweak display of the code snippets when following an example\n.bs-example + .highlight,\n.bs-example + .bs-clipboard + .highlight {\n  margin: -15px -15px 15px;\n  border-width: 0 0 1px;\n  border-radius: 0;\n}\n\n// Make the examples and snippets not full-width\n@media (min-width: @screen-sm-min) {\n  .bs-example {\n    margin-right: 0;\n    margin-left: 0;\n    background-color: #fff;\n    border-color: #ddd;\n    border-width: 1px;\n    border-radius: 4px 4px 0 0;\n    box-shadow: none;\n  }\n  .bs-example + .highlight,\n  .bs-example + .bs-clipboard + .highlight {\n    margin-top: -16px;\n    margin-right: 0;\n    margin-left: 0;\n    border-width: 1px;\n    border-bottom-right-radius: 4px;\n    border-bottom-left-radius: 4px;\n  }\n  .bs-example + .bs-clipboard .btn-clipboard {\n    top: -15px; // due to padding .bs-example has\n    border-top-right-radius: 0;\n  }\n  .bs-example-standalone {\n    border-radius: 4px;\n  }\n}\n\n// Undo width of container\n.bs-example .container {\n  width: auto;\n}\n\n// Tweak content of examples for optimum awesome\n.bs-example > p:last-child,\n.bs-example > ul:last-child,\n.bs-example > ol:last-child,\n.bs-example > blockquote:last-child,\n.bs-example > .form-control:last-child,\n.bs-example > .table:last-child,\n.bs-example > .navbar:last-child,\n.bs-example > .jumbotron:last-child,\n.bs-example > .alert:last-child,\n.bs-example > .panel:last-child,\n.bs-example > .list-group:last-child,\n.bs-example > .well:last-child,\n.bs-example > .progress:last-child,\n.bs-example > .table-responsive:last-child > .table {\n  margin-bottom: 0;\n}\n.bs-example > p > .close {\n  float: none;\n}\n\n// Typography\n.bs-example-type .table .type-info {\n  color: #767676;\n  vertical-align: middle;\n}\n.bs-example-type .table td {\n  padding: 15px 0;\n  border-color: #eee;\n}\n.bs-example-type .table tr:first-child td {\n  border-top: 0;\n}\n.bs-example-type h1,\n.bs-example-type h2,\n.bs-example-type h3,\n.bs-example-type h4,\n.bs-example-type h5,\n.bs-example-type h6 {\n  margin: 0;\n}\n\n// Contextual background colors\n.bs-example-bg-classes p {\n  padding: 15px;\n}\n\n// Images\n.bs-example > .img-circle,\n.bs-example > .img-rounded,\n.bs-example > .img-thumbnail {\n  margin: 5px;\n}\n\n// Tables\n.bs-example > .table-responsive > .table {\n  background-color: #fff;\n}\n\n// Buttons\n.bs-example > .btn,\n.bs-example > .btn-group {\n  margin-top: 5px;\n  margin-bottom: 5px;\n}\n.bs-example > .btn-toolbar + .btn-toolbar {\n  margin-top: 10px;\n}\n\n// Forms\n.bs-example-control-sizing {\n  select,\n  input[type=\"text\"] + input[type=\"text\"] {\n    margin-top: 10px;\n  }\n}\n.bs-example-form .input-group {\n  margin-bottom: 10px;\n}\n.bs-example > textarea.form-control {\n  resize: vertical;\n}\n\n// List groups\n.bs-example > .list-group {\n  max-width: 400px;\n}\n\n// Navbars\n.bs-example .navbar:last-child {\n  margin-bottom: 0;\n}\n.bs-navbar-top-example,\n.bs-navbar-bottom-example {\n  z-index: 1;\n  padding: 0;\n  overflow: hidden; // cut the drop shadows off\n}\n.bs-navbar-top-example .navbar-header,\n.bs-navbar-bottom-example .navbar-header {\n  margin-left: 0;\n}\n.bs-navbar-top-example .navbar-fixed-top,\n.bs-navbar-bottom-example .navbar-fixed-bottom {\n  position: relative;\n  margin-right: 0;\n  margin-left: 0;\n}\n.bs-navbar-top-example {\n  padding-bottom: 45px;\n}\n.bs-navbar-top-example:after {\n  top: auto;\n  bottom: 15px;\n}\n.bs-navbar-top-example .navbar-fixed-top {\n  top: -1px;\n}\n.bs-navbar-bottom-example {\n  padding-top: 45px;\n}\n.bs-navbar-bottom-example .navbar-fixed-bottom {\n  bottom: -1px;\n}\n.bs-navbar-bottom-example .navbar {\n  margin-bottom: 0;\n}\n@media (min-width: 768px) {\n  .bs-navbar-top-example .navbar-fixed-top,\n  .bs-navbar-bottom-example .navbar-fixed-bottom {\n    position: absolute;\n  }\n}\n\n// Pagination\n.bs-example .pagination {\n  margin-top: 10px;\n  margin-bottom: 10px;\n}\n\n// Pager\n.bs-example > .pager {\n  margin-top: 0;\n}\n\n// Example modals\n.bs-example-modal {\n  background-color: #f5f5f5;\n}\n.bs-example-modal .modal {\n  position: relative;\n  top: auto;\n  right: auto;\n  bottom: auto;\n  left: auto;\n  z-index: 1;\n  display: block;\n}\n.bs-example-modal .modal-dialog {\n  left: auto;\n  margin-right: auto;\n  margin-left: auto;\n}\n\n// Example dropdowns\n.bs-example > .dropdown > .dropdown-toggle {\n  float: left;\n}\n.bs-example > .dropdown > .dropdown-menu {\n  position: static;\n  display: block;\n  margin-bottom: 5px;\n  clear: left;\n}\n\n// Example tabbable tabs\n.bs-example-tabs .nav-tabs {\n  margin-bottom: 15px;\n}\n\n// Tooltips\n.bs-example-tooltips {\n  text-align: center;\n}\n.bs-example-tooltips > .btn {\n  margin-top: 5px;\n  margin-bottom: 5px;\n}\n.bs-example-tooltip .tooltip {\n  position: relative;\n  display: inline-block;\n  margin: 10px 20px;\n  opacity: 1;\n}\n\n// Popovers\n.bs-example-popover {\n  padding-bottom: 24px;\n  background-color: #f9f9f9;\n}\n.bs-example-popover .popover {\n  position: relative;\n  display: block;\n  float: left;\n  width: 260px;\n  margin: 20px;\n}\n\n// Scrollspy demo on fixed height div\n.scrollspy-example {\n  position: relative;\n  height: 200px;\n  margin-top: 10px;\n  overflow: auto;\n}\n\n.bs-example > .nav-pills-stacked-example {\n  max-width: 300px;\n}\n\n// Simple collapse example\n#collapseExample .well {\n  margin-bottom: 0;\n}\n\n// Pseudo :focus state for showing how it looks in the docs\n#focusedInput {\n  border-color: rgb(204, 204, 204); // Restate unfocused value to make CSSLint happy that there's a pre-CSS3 fallback\n  border-color: rgba(82, 168, 236, .8);\n  outline: 0;\n  outline: thin dotted \\9; // IE6-9\n  box-shadow: 0 0 8px rgba(82, 168, 236, .6);\n}\n", "// Callouts\n//\n// Not quite alerts, but custom and helpful notes for folks reading the docs.\n// Requires a base and modifier class.\n\n.bs-callout {\n  padding: 20px;\n  margin: 20px 0;\n  border: 1px solid #eee;\n  border-left-width: 5px;\n  border-radius: 3px;\n\n  h4 {\n    margin-top: 0;\n    margin-bottom: 5px;\n  }\n\n  p:last-child {\n    margin-bottom: 0;\n  }\n\n  code {\n    border-radius: 3px;\n  }\n\n  + .bs-callout {\n    margin-top: -5px;\n  }\n}\n\n.bs-callout-danger {\n  border-left-color: #ce4844;\n\n  h4 {\n    color: #ce4844;\n  }\n}\n\n.bs-callout-warning {\n  border-left-color: #aa6708;\n\n  h4 {\n    color: #aa6708;\n  }\n}\n\n.bs-callout-info {\n  border-left-color: #1b809e;\n\n  h4 {\n    color: #1b809e;\n  }\n}\n", ".color-swatches {\n  margin: 0 -5px;\n  overflow: hidden; /* clearfix */\n}\n.color-swatch {\n  float: left;\n  width: 60px;\n  height: 60px;\n  margin: 0 5px;\n  border-radius: 3px;\n}\n\n@media (min-width: 768px) {\n  .color-swatch {\n    width: 100px;\n    height: 100px;\n  }\n}\n\n// Framework colors\n.color-swatches .gray-darker {\n  background-color: #222;\n}\n.color-swatches .gray-dark {\n  background-color: #333;\n}\n.color-swatches .gray {\n  background-color: #555;\n}\n.color-swatches .gray-light {\n  background-color: #999;\n}\n.color-swatches .gray-lighter {\n  background-color: #eee;\n}\n.color-swatches .brand-primary {\n  background-color: #337ab7;\n}\n.color-swatches .brand-success {\n  background-color: #5cb85c;\n}\n.color-swatches .brand-warning {\n  background-color: #f0ad4e;\n}\n.color-swatches .brand-danger {\n  background-color: #d9534f;\n}\n.color-swatches .brand-info {\n  background-color: #5bc0de;\n}\n\n// Docs colors\n.color-swatches .bs-purple {\n  background-color: #563d7c;\n}\n.color-swatches .bs-purple-light {\n  background-color: #c7bfd3;\n}\n.color-swatches .bs-purple-lighter {\n  background-color: #e5e1ea;\n}\n.color-swatches .bs-gray {\n  background-color: #f9f9f9;\n}\n", ".bs-team .team-member {\n  line-height: 32px;\n  color: #555;\n}\n.bs-team .team-member:hover {\n  color: #333;\n  text-decoration: none;\n}\n.bs-team .github-btn {\n  float: right;\n  width: 180px;\n  height: 20px;\n  margin-top: 6px;\n  border: none;\n}\n.bs-team img {\n  float: left;\n  width: 32px;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n", "// stylelint-disable selector-no-qualifying-type, declaration-no-important\n\n// Responsive (scrollable) doc tables\n.table-responsive .highlight pre {\n  white-space: normal;\n}\n\n// Utility classes table\n.bs-table th small,\n.responsive-utilities th small {\n  display: block;\n  font-weight: 400;\n  color: #999;\n}\n.responsive-utilities tbody th {\n  font-weight: 400;\n}\n.responsive-utilities td {\n  text-align: center;\n}\n.responsive-utilities td.is-visible {\n  color: #468847;\n  background-color: #dff0d8 !important;\n}\n.responsive-utilities td.is-hidden {\n  color: #ccc;\n  background-color: #f9f9f9 !important;\n}\n\n// Responsive tests\n.responsive-utilities-test {\n  margin-top: 5px;\n}\n.responsive-utilities-test .col-xs-6 {\n  margin-bottom: 10px;\n}\n.responsive-utilities-test span {\n  display: block;\n  padding: 15px 10px;\n  font-size: 14px;\n  font-weight: 700;\n  line-height: 1.1;\n  text-align: center;\n  border-radius: 4px;\n}\n.visible-on .col-xs-6 .hidden-xs,\n.visible-on .col-xs-6 .hidden-sm,\n.visible-on .col-xs-6 .hidden-md,\n.visible-on .col-xs-6 .hidden-lg,\n.hidden-on .col-xs-6 .hidden-xs,\n.hidden-on .col-xs-6 .hidden-sm,\n.hidden-on .col-xs-6 .hidden-md,\n.hidden-on .col-xs-6 .hidden-lg {\n  color: #999;\n  border: 1px solid #ddd;\n}\n.visible-on .col-xs-6 .visible-xs-block,\n.visible-on .col-xs-6 .visible-sm-block,\n.visible-on .col-xs-6 .visible-md-block,\n.visible-on .col-xs-6 .visible-lg-block,\n.hidden-on .col-xs-6 .visible-xs-block,\n.hidden-on .col-xs-6 .visible-sm-block,\n.hidden-on .col-xs-6 .visible-md-block,\n.hidden-on .col-xs-6 .visible-lg-block {\n  color: #468847;\n  background-color: #dff0d8;\n  border: 1px solid #d6e9c6;\n}\n", ".bs-glyphicons {\n  margin: 0 -10px 20px;\n  overflow: hidden;\n}\n.bs-glyphicons-list {\n  padding-left: 0;\n  list-style: none;\n}\n.bs-glyphicons li {\n  float: left;\n  width: 25%;\n  height: 115px;\n  padding: 10px;\n  font-size: 10px;\n  line-height: 1.4;\n  text-align: center;\n  background-color: #f9f9f9;\n  border: 1px solid #fff;\n}\n.bs-glyphicons .glyphicon {\n  margin-top: 5px;\n  margin-bottom: 10px;\n  font-size: 24px;\n}\n.bs-glyphicons .glyphicon-class {\n  display: block;\n  text-align: center;\n  word-wrap: break-word; // Help out IE10+ with class names\n}\n.bs-glyphicons li:hover {\n  color: #fff;\n  background-color: #563d7c;\n}\n\n@media (min-width: 768px) {\n  .bs-glyphicons {\n    margin-right: 0;\n    margin-left: 0;\n  }\n  .bs-glyphicons li {\n    width: 12.5%;\n    font-size: 12px;\n  }\n}\n", "// stylelint-disable selector-max-id, selector-no-qualifying-type\n\n.bs-customizer .toggle {\n  float: right;\n  margin-top: 25px;\n}\n\n// Headings and form contrls\n.bs-customizer label {\n  margin-top: 10px;\n  font-weight: 500;\n  color: #555;\n}\n.bs-customizer h2 {\n  padding-top: 30px;\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n.bs-customizer h3 {\n  margin-bottom: 0;\n}\n.bs-customizer h4 {\n  margin-top: 15px;\n  margin-bottom: 0;\n}\n.bs-customizer .bs-callout h4 {\n  margin-top: 0; // lame, but due to specificity we have to duplicate\n  margin-bottom: 5px;\n}\n.bs-customizer input[type=\"text\"] {\n  font-family: Menlo, Monaco, Consolas, \"Courier New\", monospace;\n  background-color: #fafafa;\n}\n.bs-customizer .help-block {\n  margin-bottom: 5px;\n  font-size: 12px;\n}\n\n// For the variables, use regular weight\n#less-section label {\n  font-weight: 400;\n}\n\n// Downloads\n.bs-customize-download .btn-outline {\n  padding: 20px;\n}\n\n// Error handling\n.bs-customizer-alert {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1030;\n  padding: 15px 0;\n  color: #fff;\n  background-color: #d9534f;\n  border-bottom: 1px solid #b94441;\n  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .25);\n}\n.bs-customizer-alert .close {\n  margin-top: -4px;\n  font-size: 24px;\n}\n.bs-customizer-alert p {\n  margin-bottom: 0;\n}\n.bs-customizer-alert .glyphicon {\n  margin-right: 5px;\n}\n.bs-customizer-alert pre {\n  margin: 10px 0 0;\n  color: #fff;\n  background-color: #a83c3a;\n  border-color: #973634;\n  box-shadow: inset 0 2px 4px rgba(0, 0, 0, .05), 0 1px 0 rgba(255, 255, 255, .1);\n}\n\n.bs-dropzone {\n  position: relative;\n  padding: 20px;\n  margin-bottom: 20px;\n  color: #777;\n  text-align: center;\n  border: 2px dashed #eee;\n  border-radius: 4px;\n}\n.bs-dropzone .import-header {\n  margin-bottom: 5px;\n}\n.bs-dropzone .glyphicon-folder-open {\n  font-size: 40px;\n}\n.bs-dropzone hr {\n  width: 100px;\n}\n.bs-dropzone .lead {\n  margin-bottom: 10px;\n  font-weight: 400;\n  color: #333;\n}\n#import-manual-trigger {\n  cursor: pointer;\n}\n.bs-dropzone p:last-child {\n  margin-bottom: 0;\n}\n", "// Logo series wrapper\n.bs-brand-logos {\n  display: table;\n  width: 100%;\n  margin-bottom: 15px;\n  overflow: hidden;\n  color: #563d7c;\n  background-color: #f9f9f9;\n  border-radius: 4px;\n}\n\n// Individual items\n.bs-brand-item {\n  padding: 60px 0;\n  text-align: center;\n}\n.bs-brand-item + .bs-brand-item {\n  border-top: 1px solid #fff;\n}\n.bs-brand-logos .inverse {\n  color: #fff;\n  background-color: #563d7c;\n}\n\n// Heading content within\n.bs-brand-item h1,\n.bs-brand-item h3 {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.bs-brand-item .bs-docs-booticon {\n  margin-right: auto;\n  margin-left: auto;\n}\n\n// Make the icons stand out on what is/isn't okay\n.bs-brand-item .glyphicon {\n  width: 30px;\n  height: 30px;\n  margin: 10px auto -10px;\n  line-height: 30px;\n  color: #fff;\n  border-radius: 50%;\n}\n.bs-brand-item .glyphicon-ok {\n  background-color: #5cb85c;\n}\n.bs-brand-item .glyphicon-remove {\n  background-color: #d9534f;\n}\n\n@media (min-width: @screen-sm-min) {\n  .bs-brand-item {\n    display: table-cell;\n    width: 1%;\n  }\n  .bs-brand-item + .bs-brand-item {\n    border-top: 0;\n    border-left: 1px solid #fff;\n  }\n  .bs-brand-item h1 {\n    font-size: 60px;\n  }\n}\n", "// clipboard.js\n//\n// JS-based `Copy` buttons for code snippets.\n\n.bs-clipboard {\n  position: relative;\n  display: none;\n  float: right;\n\n  + .highlight {\n    margin-top: 0;\n  }\n}\n\n.btn-clipboard {\n  position: absolute;\n  top: 0;\n  right: 0;\n  z-index: 10;\n  display: block;\n  padding: 4px 8px;\n  font-size: 12px;\n  color: #818a91;\n  cursor: pointer;\n  background-color: transparent;\n  border: 0;\n  border-top-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n\n  &:hover {\n    color: #fff;\n    background-color: #027de7;\n  }\n}\n\n@media (min-width: @screen-sm-min) {\n  .bs-clipboard {\n    display: block;\n  }\n}\n", ".anchorjs-link {\n  color: inherit;\n}\n\n@media (max-width: 480px) {\n  .anchorjs-link {\n    display: none;\n  }\n}\n\n*:hover > .anchorjs-link {\n  opacity: .75;\n  transition: color .16s linear;\n}\n\n*:hover > .anchorjs-link:hover,\n.anchorjs-link:focus {\n  text-decoration: none;\n  opacity: 1;\n}\n", "// stylelint-disable declaration-no-important\n\n// Docsearch overrides\n//\n// `!important` indicates overridden properties.\n.algolia-autocomplete {\n  display: block !important;\n\n  // Menu container\n  .ds-dropdown-menu {\n    width: 100%;\n    min-width: 0 !important;\n    max-width: none !important;\n    padding: 10px 0 !important;\n    background-color: #fff;\n    background-clip: padding-box;\n    border: 1px solid #ddd;\n    border: 1px solid rgba(0, 0, 0, .1);\n    box-shadow: 0 8px 15px rgba(0, 0, 0, .175);\n\n    @media (min-width: @screen-sm-min) {\n      width: 175%;\n    }\n\n    // Caret\n    &:before {\n      display: none !important;\n    }\n\n    [class^=\"ds-dataset-\"] {\n      padding: 0 !important;\n      overflow: visible !important;\n      background-color: transparent !important;\n      border: 0 !important;\n    }\n\n    .ds-suggestions {\n      margin-top: 0 !important;\n    }\n\n    .ds-input {\n      box-shadow: none;\n    }\n  }\n\n  .algolia-docsearch-suggestion {\n    padding: 0 !important;\n    overflow: visible !important;\n  }\n\n  .algolia-docsearch-suggestion--category-header {\n    padding: 2px 15px !important;\n    margin-top: 0 !important;\n    font-size: 13px !important;\n    font-weight: 500 !important;\n    color: #7952b3 !important;\n    border-bottom: 0 !important;\n  }\n\n  .algolia-docsearch-suggestion--wrapper {\n    float: none !important;\n    padding-top: 0 !important;\n  }\n\n  // Section header\n  .algolia-docsearch-suggestion--subcategory-column {\n    float: none !important;\n    width: auto !important;\n    padding: 0 !important;\n    text-align: left !important;\n  }\n\n  .algolia-docsearch-suggestion--content {\n    float: none !important;\n    width: auto !important;\n    padding: 0 !important;\n\n    // Vertical divider between column header and content\n    &:before {\n      display: none !important;\n    }\n  }\n\n  .ds-suggestion {\n    &:not(:first-child) {\n      .algolia-docsearch-suggestion--category-header {\n        padding-top: 10px !important;\n        margin-top: 10px !important;\n        border-top: 1px solid #eee;\n      }\n    }\n\n    .algolia-docsearch-suggestion--subcategory-column {\n      display: none !important;\n    }\n  }\n\n  .algolia-docsearch-suggestion--title {\n    display: block;\n    padding: 4px 15px !important;\n    margin-bottom: 0 !important;\n    font-size: 13px !important;\n    font-weight: 400 !important;\n  }\n\n  .algolia-docsearch-suggestion--text {\n    padding: 0 15px 8px !important;\n    margin-top: -4px;\n    font-size: 13px !important;\n    font-weight: 400;\n    line-height: 1.25 !important;\n  }\n\n  .algolia-docsearch-footer {\n    float: none !important;\n    width: auto !important;\n    height: auto !important;\n    padding: 10px 15px 0;\n    font-size: 10px !important;\n    line-height: 1 !important;\n    color: #767676 !important;\n    border-top: 1px solid #eee;\n  }\n\n  .algolia-docsearch-footer--logo {\n    display: inline !important;\n    overflow: visible !important;\n    color: inherit !important;\n    text-indent: 0 !important;\n    background: none !important;\n  }\n\n  .algolia-docsearch-suggestion--highlight {\n    color: #5f2dab;\n    background-color: #eee;\n  }\n\n  .algolia-docsearch-suggestion--text .algolia-docsearch-suggestion--highlight {\n    box-shadow: inset 0 -2px 0 0 rgba(95, 45, 171, .5) !important;\n  }\n\n  .ds-suggestion.ds-cursor .algolia-docsearch-suggestion--content {\n    background-color: #e5e5e5 !important;\n  }\n}\n", "// stylelint-disable selector-max-type, selector-no-qualifying-type, declaration-no-important\n\n//\n// Misc\n//\n\n// For scrollspy\nbody {\n  position: relative;\n}\n\n// Keep code small in tables on account of limited space\n.table code {\n  font-size: 13px;\n  font-weight: 400;\n}\n\n// Inline code within headings retain the heading's background-color\nh2 code,\nh3 code,\nh4 code {\n  background-color: inherit;\n}\n\n// Space docs sections out\n.bs-docs-section {\n  margin-bottom: 60px;\n}\n.bs-docs-section:last-child {\n  margin-bottom: 0;\n}\n\nh1[id] {\n  padding-top: 20px;\n  margin-top: 0;\n}\n\n// Wall of Browser Bugs\n.bs-docs-browser-bugs td p {\n  margin-bottom: 0;\n}\n\n.bs-docs-browser-bugs th:first-child {\n  width: 18%;\n}\n\n// Don't wrap event names in Events tables in JS plugin docs\n.bs-events-table > thead > tr > th:first-child,\n.bs-events-table > tbody > tr > td:first-child {\n  white-space: nowrap;\n}\n\n.bs-events-table > thead > tr > th:first-child {\n  width: 150px;\n}\n\n.js-options-table > thead > tr > th:nth-child(1),\n.js-options-table > thead > tr > th:nth-child(2) {\n  width: 100px;\n}\n\n.js-options-table > thead > tr > th:nth-child(3) {\n  width: 50px;\n}\n\n// v4 notice above main navbar\n.v4-tease {\n  display: block;\n  padding: 15px 20px;\n  font-weight: 700;\n  color: #fff;\n  text-align: center;\n  background-color: #0275d8;\n\n  &:focus,\n  &:hover {\n    color: #fff;\n    text-decoration: none;\n    background-color: #0269c2;\n  }\n}\n\n/* Nullify ill-advised printing of hrefs; see #18711 */\n@media print {\n  a[href]:after {\n    content: \"\" !important;\n  }\n}\n"]}