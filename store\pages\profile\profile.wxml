<!-- pages/profile/profile.wxml -->
<view class="container">
  <form bindsubmit="submitForm">
    <!-- 头像 -->
    <view class="form-item">
      <view class="label">头像</view>
      <view class="value">
        <image 
          src="{{userInfo.avatar_url || '/images/default-avatar.png'}}" 
          class="avatar" 
          bindtap="chooseAvatar"
        ></image>
      </view>
    </view>
    
    <!-- 用户名 -->
    <view class="form-item">
      <view class="label">用户名</view>
      <view class="value">{{userInfo.username}}</view>
    </view>
    
    <!-- 性别 -->
    <view class="form-item">
      <view class="label">性别</view>
      <view class="value">
        <radio-group bindchange="onSexChange">
          <block wx:for="{{sexOptions}}" wx:key="value">
            <label class="radio-label">
              <radio value="{{item.value}}" checked="{{userInfo.sex == item.value}}"/>
              {{item.label}}
            </label>
          </block>
        </radio-group>
      </view>
    </view>
    
    <!-- 手机号 -->
    <view class="form-item">
      <view class="label">手机号</view>
      <view class="value">
        <input 
          type="number" 
          placeholder="请输入手机号" 
          value="{{userInfo.phone}}" 
          bindinput="onPhoneChange"
        />
      </view>
    </view>
    
    <!-- 省份选择 -->
    <view class="form-item">
      <view class="label">所在省份</view>
      <view class="value">
        <picker 
          mode="selector" 
          range="{{provinces}}" 
          value="{{provinceIndex}}"
          bindchange="bindProvinceChange"
        >
          <view class="picker">
            {{userInfo.address || '请选择省份'}}
          </view>
        </picker>
      </view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="form-submit">
      <button form-type="submit" type="primary">保存修改</button>
    </view>
  </form>
</view>