<div class="bs-docs-section">
  <h1 id="jumbotron" class="page-header">Jumbotron</h1>

  <p>A lightweight, flexible component that can optionally extend the entire viewport to showcase key content on your site.</p>
  <div class="bs-example" data-example-id="simple-jumbotron">
    <div class="jumbotron">
      <h1>Hello, world!</h1>
      <p>This is a simple hero unit, a simple jumbotron-style component for calling extra attention to featured content or information.</p>
      <p><a class="btn btn-primary btn-lg" href="#" role="button">Learn more</a></p>
    </div>
  </div>
{% highlight html %}
<div class="jumbotron">
  <h1>Hello, world!</h1>
  <p>...</p>
  <p><a class="btn btn-primary btn-lg" href="#" role="button">Learn more</a></p>
</div>
{% endhighlight %}
  <p>To make the jumbotron full width, and without rounded corners, place it outside all <code>.container</code>s and instead add a <code>.container</code> within.</p>
{% highlight html %}
<div class="jumbotron">
  <div class="container">
    ...
  </div>
</div>
{% endhighlight %}
</div>
