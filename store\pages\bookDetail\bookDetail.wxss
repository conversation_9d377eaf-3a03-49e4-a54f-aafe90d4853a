
.container {
  padding: 20rpx;
  position: relative;
  padding-bottom: 120rpx; 
}


.book-header {
  display: flex;
  margin-bottom: 30rpx;
}

.book-cover {
  width: 250rpx;
  height: 350rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.book-info {
  flex: 1;
  margin-left: 30rpx;
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.price {
  font-size: 32rpx;
  color: #e74c3c;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.author, .stock {
  font-size: 28rpx;
  color: rgb(5, 1, 1);
  margin-bottom: 8rpx;
}

.meta {
  font-size: 26rpx;
  color: rgb(0, 0, 0);
  margin-top: 10rpx;
}


.tabs {
  display: flex;
  border-bottom: 1rpx solid #eee;
  padding-bottom: 20rpx;
  margin-bottom: 30rpx;
}

.tab {
  font-size: 30rpx;
  color: #666;
  padding: 10rpx 0;
  position: relative;
}

.tab.active {
  color: #1aad19;
  font-weight: bold;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 0;
  width: 100%;
  height: 4rpx;
  background-color: #1aad19;
  border-radius: 2rpx;
}


.content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}


.footer {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  z-index: 100;
}

.btn.borrow {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: #1aad19;
  color: white;
  font-size: 28rpx;
  box-shadow: 0 4rpx 20rpx rgba(26, 173, 25, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.btn.borrow:active {
  background-color: #179116;
  transform: scale(0.95);
}


.btn.borrow {
  transition: all 0.2s ease;
}

/* 新增书评卡片样式 */
.comments-container {
  padding: 20rpx;
}

.comment-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.comment-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  margin-bottom: 16rpx;
}

.comment-meta {
  display: flex;
  justify-content: flex-end;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

.empty-comments {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}