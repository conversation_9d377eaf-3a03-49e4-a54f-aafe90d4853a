
.container {
  padding: 20rpx;
  position: relative;
  padding-bottom: 120rpx; 
}


.book-header {
  display: flex;
  margin-bottom: 30rpx;
}

.book-cover {
  width: 250rpx;
  height: 350rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.book-info {
  flex: 1;
  margin-left: 30rpx;
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.price {
  font-size: 32rpx;
  color: #e74c3c;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.author, .stock {
  font-size: 28rpx;
  color: rgb(5, 1, 1);
  margin-bottom: 8rpx;
}

.meta {
  font-size: 26rpx;
  color: rgb(0, 0, 0);
  margin-top: 10rpx;
}


.tabs {
  display: flex;
  border-bottom: 1rpx solid #eee;
  padding-bottom: 20rpx;
  margin-bottom: 30rpx;
}

.tab {
  font-size: 30rpx;
  color: #666;
  padding: 10rpx 0;
  position: relative;
}

.tab.active {
  color: #1aad19;
  font-weight: bold;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 0;
  width: 100%;
  height: 4rpx;
  background-color: #1aad19;
  border-radius: 2rpx;
}


.content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}


.footer {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  z-index: 100;
}

.btn.borrow {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: #1aad19;
  color: white;
  font-size: 28rpx;
  box-shadow: 0 4rpx 20rpx rgba(26, 173, 25, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.btn.borrow:active {
  background-color: #179116;
  transform: scale(0.95);
}


.btn.borrow {
  transition: all 0.2s ease;
}

/* 评论相关样式 */
.comments-container {
  padding: 20rpx;
}

/* 添加评论区域 */
.add-comment-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.comment-input {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  font-size: 28rpx;
  line-height: 1.5;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.comment-input:focus {
  border-color: #1aad19;
}

.submit-comment-btn {
  width: 100%;
  height: 80rpx;
  background-color: #1aad19;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-comment-btn[disabled] {
  background-color: #ccc;
  color: #999;
}

.submit-comment-btn:not([disabled]):active {
  background-color: #179116;
}

/* 评论列表 */
.comments-list {
  margin-top: 20rpx;
}

.comment-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.comment-user {
  font-size: 26rpx;
  color: #1aad19;
  font-weight: bold;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

.comment-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

.empty-comments {
  text-align: center;
  padding: 60rpx 40rpx;
  color: #999;
  font-size: 28rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}