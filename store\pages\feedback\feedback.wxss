/* pages/feedback/feedback.wxss */
.feedback-container {
  padding: 40rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.header {
  margin-bottom: 60rpx;
  text-align: center;
}

.title {
  font-size: 44rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  letter-spacing: 1rpx;
}

.subtitle {
  font-size: 30rpx;
  color: #7f8c8d;
  letter-spacing: 0.5rpx;
}

.conr {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08);
}

.form-group {
  margin-bottom: 30rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  background-color: rgba(255, 255, 255, 0.7);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.form-label {
  display: block;
  font-size: 32rpx;
  color: #34495e;
  margin-bottom: 20rpx;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  position: relative;
  padding-left: 15rpx;
}

.form-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 10rpx;
  height: 32rpx;
  width: 6rpx;
  background: #3498db;
  border-radius: 3rpx;
}

.picker {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  color: #333;
  font-size: 30rpx;
  border: 1rpx solid #e0e0e0;
}

.picker-content {
  padding: 10rpx 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-arrow {
  color: #95a5a6;
  font-size: 24rpx;
}

.textarea {
  width: 100%;
  min-height: 300rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  box-sizing: border-box;
  border: 1rpx solid #e0e0e0;
}

.word-count {
  display: block;
  text-align: right;
  font-size: 26rpx;
  color: #95a5a6;
  margin-top: 10rpx;
}

.input {
  width: 100%;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 30rpx;
  color: #333;
  border: 1rpx solid #e0e0e0;
}

.submit-btn {
  margin-top: 60rpx;
  background: linear-gradient(135deg, #3498db 0%, #2ecc71 100%);
  color: white;
  border-radius: 50rpx;
  font-size: 34rpx;
  height: 100rpx;
  line-height: 100rpx;
  box-shadow: 0 8rpx 20rpx rgba(46, 204, 113, 0.3);
  transition: all 0.3s;
  letter-spacing: 2rpx;
  border: none;
}

.submit-btn[disabled] {
  background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
  box-shadow: none;
  opacity: 0.8;
}

.history-link {
  margin-top: 50rpx;
  text-align: center;
  color: #3498db;
  font-size: 30rpx;
  text-decoration: none;
  padding: 25rpx 0;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  position: relative;
}

.history-link::after {
  content: '';
  position: absolute;
  bottom: 15rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 120rpx;
  height: 2rpx;
  background: #3498db;
  transition: all 0.3s;
}

.history-link:active::after {
  width: 150rpx;
}