const app = getApp();
Page({
  data: {
    amount: 0
  },
  inputAmount(e) {
    this.setData({ amount: e.detail.value });
  },
  recharge() {
    wx.request({
      url: 'http://127.0.0.1:8000/recharge/', // Replace with your backend URL
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      data: {
        amount: this.data.amount
      },
      success: (res) => {
        if (res.data.code === 1) {
          wx.showToast({ title: '充值成功', icon: 'success' });
          // wx.navigateBack();
        }
      }
    });
  },
  

});
