Page({
  data: {
    username: '',
    password: '',
    confirmPassword: ''
  },

  onUsernameInput(e) {
    this.setData({
      username: e.detail.value
    });
  },

  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    });
  },

  onConfirmPasswordInput(e) {
    this.setData({
      confirmPassword: e.detail.value
    });
  },

  async res() {
    const { username, password, confirmPassword } = this.data;

    if (password !== confirmPassword) {
      wx.showToast({
        title: '两次密码不相同',
        icon: 'none'
      });
      return;
    }

    try {
      const response = await wx.request({
        url: 'http://127.0.0.1:8000/register/',
        method: 'POST',
        data: {
          username: username,
          password: password
        },
        success(res) {
          console.log(res);
          if (res.data.code === 1) {
            wx.showToast({
              title: '注册成功',
              icon: 'success'
            });
            wx.navigateTo({
              url: '/pages/login/login',
            });
          } else {
            wx.showToast({
              title: res.data.msg,
              icon: 'none'
            });
          }
        },
        fail(error) {
          wx.showToast({
            title: '注册失败',
            icon: 'none'
          });
        }
      });
    } catch (error) {
      wx.showToast({
        title: '注册失败',
        icon: 'none'
      });
    }
  }
});
