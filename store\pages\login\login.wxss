/* 容器样式 */
.container {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* 内容容器 */
.ctn {
  width: 80%;
  padding: 40rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(5px); /* 毛玻璃效果 */
  margin-top: -100rpx;
}

/* 标题样式 */
.title {
  font-size: 48rpx;
  color: #333;
  text-align: center;
  margin-bottom: 60rpx;
  font-weight: bold;
  text-shadow: 1rpx 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

/* 输入框组 */
.input-group {
  margin-bottom: 40rpx;
}

/* 输入框样式 */
input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  background-color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  box-sizing: border-box;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #4a90e2;
  color: white;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
}

/* 注册按钮 */
.res-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #f8f8f8;
  color: #4a90e2;
  border-radius: 10rpx;
  font-size: 32rpx;
  border: 1rpx solid #4a90e2;
}

/* 按钮点击效果 */
button:active {
  opacity: 0.8;
  transform: scale(0.98);
}