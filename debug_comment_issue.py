#!/usr/bin/env python3
"""
调试评论问题
"""
import requests
import json
import time

def debug_comment_issue():
    """调试评论问题"""
    
    base_url = "http://127.0.0.1:8000"
    
    print("=== 评论问题调试 ===")
    
    # 1. 用户登录
    print("1. 用户登录...")
    login_data = {"username": "root0", "password": "123456"}
    login_response = requests.post(f"{base_url}/login/", json=login_data)
    
    if login_response.status_code == 200 and login_response.json().get('code') == 1:
        token = login_response.json()['data']['access_token']
        print(f"✅ 登录成功")
        
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        book_id = 1  # 测试图书ID
        
        # 2. 获取评论前的状态
        print(f"\n2. 获取图书{book_id}评论前的状态...")
        before_response = requests.get(f"{base_url}/books/{book_id}/", headers=headers)
        
        if before_response.status_code == 200:
            before_data = before_response.json()
            before_count = len(before_data['data']['comments'])
            print(f"评论前数量: {before_count}")
            
            if before_data['data']['comments']:
                latest_before = before_data['data']['comments'][0]
                print(f"最新评论ID: {latest_before.get('id', 'None')}")
                print(f"最新评论内容: {latest_before.get('content', 'None')[:30]}...")
        
        # 3. 添加新评论
        print(f"\n3. 添加新评论...")
        test_content = f"测试评论 - {time.strftime('%Y-%m-%d %H:%M:%S')}"
        comment_data = {"content": test_content}
        
        comment_response = requests.post(
            f"{base_url}/books/{book_id}/comments/", 
            json=comment_data, 
            headers=headers
        )
        
        print(f"评论提交状态码: {comment_response.status_code}")
        print(f"评论提交响应: {comment_response.text}")
        
        if comment_response.status_code == 201:
            comment_result = comment_response.json()
            new_comment_id = comment_result['data']['id']
            print(f"✅ 新评论ID: {new_comment_id}")
            
            # 4. 立即获取评论后的状态
            print(f"\n4. 获取评论后的状态...")
            after_response = requests.get(f"{base_url}/books/{book_id}/", headers=headers)
            
            if after_response.status_code == 200:
                after_data = after_response.json()
                after_count = len(after_data['data']['comments'])
                print(f"评论后数量: {after_count}")
                
                if after_data['data']['comments']:
                    latest_after = after_data['data']['comments'][0]
                    print(f"最新评论ID: {latest_after.get('id', 'None')}")
                    print(f"最新评论内容: {latest_after.get('content', 'None')[:30]}...")
                    
                    # 检查新评论是否在列表中
                    found_new_comment = False
                    for comment in after_data['data']['comments']:
                        if comment.get('id') == new_comment_id:
                            found_new_comment = True
                            print(f"✅ 找到新评论: ID {new_comment_id}")
                            break
                    
                    if not found_new_comment:
                        print(f"❌ 新评论ID {new_comment_id} 未在列表中找到")
                        print("前10条评论ID:")
                        for i, comment in enumerate(after_data['data']['comments'][:10]):
                            print(f"  {i+1}. ID: {comment.get('id')}, 内容: {comment.get('content', '')[:20]}...")
                
                # 5. 检查数据库
                print(f"\n5. 检查数据库中的评论...")
                check_db_comment(new_comment_id)
            
        else:
            print(f"❌ 评论提交失败")
    else:
        print("❌ 登录失败")

def check_db_comment(comment_id):
    """检查数据库中的特定评论"""
    import os
    import sys
    import django
    
    # 设置Django环境
    sys.path.append('un')
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'un.settings')
    django.setup()
    
    from customer.models import Bookcomment
    
    try:
        comment = Bookcomment.objects.get(id=comment_id)
        print(f"✅ 数据库中找到评论:")
        print(f"   ID: {comment.id}")
        print(f"   图书: {comment.book.name}")
        print(f"   用户: {comment.user.username}")
        print(f"   内容: {comment.content}")
        print(f"   时间: {comment.create_time}")
    except Bookcomment.DoesNotExist:
        print(f"❌ 数据库中未找到评论ID {comment_id}")

if __name__ == "__main__":
    debug_comment_issue()
