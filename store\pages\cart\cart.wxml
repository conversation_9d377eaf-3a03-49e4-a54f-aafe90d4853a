<view class="container">
  <!-- 购物车列表 -->
  <view class="cart-list">
  <block wx:for="{{cart.items}}" wx:key="id">
    <view class="cart-item">
      <image src="{{item.product_image}}" class="product-image"></image>
      <view class="product-info">
        <text class="product-name">{{item.product_name}}</text>
        <text class="product-price">¥{{item.product_price}}</text>
      </view>
      <button class="btn delete" bindtap="removeItem" data-id="{{item.id}}">删除</button>
    </view>
  </block>
</view>

  <!-- 空购物车提示 -->
  <view wx:if="{{cart.total_items === 0}}" class="empty-cart">
    <text>购物车空空如也</text>
    <button class="btn browse" bindtap="goToBookList">去逛逛</button>
  </view>

  <!-- 结算栏 -->
  <view wx:if="{{cart.total_items > 0}}" class="checkout-bar">
    <view class="total">
      <text>合计: </text>
      <text class="total-price">¥{{cart.total_price}}</text>
    </view>
    <button class="btn checkout" bindtap="placeOrder">结算({{cart.total_items}})</button>
  </view>
</view>