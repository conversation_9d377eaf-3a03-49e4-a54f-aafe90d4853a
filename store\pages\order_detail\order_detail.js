const app = getApp();
Page({
  data: {
    order: null
  },

  fetchOrderDetails: function (orderNum) {
    wx.request({
      url: `http://127.0.0.1:8000/order_detail/${orderNum}/`,
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log(res.data.data);
        if (res.data.code === 1) {
          this.setData({
            order: res.data.data
          });
        } else {
          wx.showToast({
            title: '加载失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  onLoad: function (options) {
    const orderNum = options.order_num;
    this.fetchOrderDetails(orderNum);
  },

  onProductClick: function (event) {
    const productId = event.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/bookDetail/bookDetail?id=${productId}` 
    });
  },
});
