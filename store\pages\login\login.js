Page({
  data: {
    username: '',
    password: '',
  },

  // 处理用户名输入
  onUsernameInput(e) {
    this.setData({
      username: e.detail.value,
    });
  },

  // 处理密码输入
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value,
    });
  },

  res(){
    wx.navigateTo({
      url: '/pages/resigter/resigter',
    })
  },

  // 点击登录按钮
  onLogin() {
    const { username, password } = this.data;

    if (!username || !password) {
      wx.showToast({
        title: '用户名和密码不能为空',
        icon: 'none',
      });
      return;
    }

    const app = getApp();

    // 调用 app.js 中封装的登录函数
    app.login(username, password, 
      (res) => {
        // 登录成功
        wx.showToast({
          title: '登录成功',
          icon: 'success',
        });
        // 登录成功后跳转到首页
        wx.reLaunch({
          url: '/pages/home/<USER>',  // 替换为你的小程序首页路径
        });
      },
      (err) => {
        // 登录失败
        wx.showToast({
          title: '登录失败，请检查用户名或密码',
          icon: 'none',
        });
        console.error(err);
      }
    );
  },
});
