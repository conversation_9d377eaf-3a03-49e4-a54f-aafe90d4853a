<!-- pages/books/books.wxml -->
<view class="container">
  <!-- 分类导航 -->
  <scroll-view class="category-nav" scroll-x>
    <block wx:for="{{categories}}" wx:key="id">
      <view 
        class="category-item {{activeCategoryId === item.id ? 'active' : ''}}" 
        bindtap="switchCategory"
        data-id="{{item.id}}"
      >
        {{item.name}}
      </view>
    </block>
  </scroll-view>

  <!-- 图书列表 -->
  <view class="book-list {{books.length === 1 ? 'single-book' : ''}}">
    <block wx:for="{{books}}" wx:key="id">
      <view class="book-item" bindtap="navigateToDetail" data-id="{{item.id}}">
        <image class="book-cover" src="{{item.img_url}}" mode="{{books.length === 1 ? 'widthFix' : 'aspectFill'}}"></image>
        <view class="book-info">
          <text class="book-title">{{item.name}}</text>
          <text class="book-author">{{item.author}}</text>
          <text class="book-price">¥{{item.price}}</text>
          <text class="book-category">{{item.category_name}}</text>
        </view>
      </view>
    </block>
  </view>


  
</view>