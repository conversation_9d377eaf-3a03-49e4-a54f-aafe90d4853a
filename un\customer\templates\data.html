{% extends 'base.html' %}
{% block content %}
{% load static %}

<style>

.container {
 max-width:80%;
    margin: 50px auto;
    background-color: #ffffff;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding: 30px;
    overflow: hidden;
    transition: transform 0.3s ease;
    margin-top: 80px;
}

/* 页头 */
h1 {
    text-align: center;
    font-size: 32px;
    color: #2d3b4e;
    font-weight: bold;
    margin-bottom: 30px;
}

/* 子标题 */
h2 {
    font-size: 28px;
    color: #2d3b4e;
    margin-bottom: 20px;
    font-weight: 500;
    text-align: center;
}

/* 统计信息 */
ul {
    list-style: none;
    margin-bottom: 30px;
    padding: 0;
}

ul li {
    font-size: 20px;
    color: #555;
    margin: 10px 0;
    display: flex;
    justify-content: space-between;
    padding: 12px 18px;
    background-color: #f9fafb;
    border-radius: 6px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

ul li span {
    font-weight: bold;
    color: #2d3b4e;
}

/* 图表容器 */
#bar-chart, #pie-chart {
    width: 100%;
    height: 450px;
    border-radius: 12px;
    margin: 30px 0;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;
}

/* 动效 */
.container:hover {
    transform: translateY(-10px);
}

#bar-chart:hover, #pie-chart:hover {
    transform: scale(1.02);
}

/* 按钮样式 */
button {
    padding: 12px 24px;
    font-size: 16px;
    color: #ffffff;
    background-color: #007bff;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: #0056b3;
}

/* 表单输入框 */
input[type="text"] {
    padding: 12px;
    font-size: 16px;
    width: 100%;
    max-width: 400px;
    margin: 15px 0;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-sizing: border-box;
    outline: none;
    transition: border-color 0.3s ease;
}

input[type="text"]:focus {
    border-color: #007bff;
}

/* 链接 */
a {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

a:hover {
    color: #0056b3;
}

/* 美化列表 */
li {
    background-color: #f9f9f9;
    border-radius: 6px;
    margin-bottom: 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    padding: 12px 18px;
    display: flex;
    justify-content: space-between;
}

/* 美化ul */
ul {
    padding: 0;
    margin: 0;
}

</style>


<div class="container">
    <h1>数据统计</h1>

<div style="display: flex; gap: 20px; margin: 20px 0;">
      <div style="display: flex; gap: 15px; width: 100%;">
        <div style="flex: 1; background: #e3f2fd; border-radius: 8px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
            <h3 style="margin: 0 0 10px 0; color: #1976d2;">用户数</h3>
            <p style="font-size: 1.5rem; margin: 0; font-weight: bold;">{{ user_count }}</p>
        </div>
        <div style="flex: 1; background: #e8f5e9; border-radius: 8px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
            <h3 style="margin: 0 0 10px 0; color: #388e3c;">图书数</h3>
            <p style="font-size: 1.5rem; margin: 0; font-weight: bold;">{{ book_count }}</p>
        </div>
        <div style="flex: 1; background: #fff3e0; border-radius: 8px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
            <h3 style="margin: 0 0 10px 0; color: #ffa000;">订单数</h3>
            <p style="font-size: 1.5rem; margin: 0; font-weight: bold;">{{ order_count }}</p>
        </div>
        <div style="flex: 1; background: #fce4ec; border-radius: 8px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
            <h3 style="margin: 0 0 10px 0; color: #d81b60;">反馈数</h3>
            <p style="font-size: 1.5rem; margin: 0; font-weight: bold;">{{ feedback_count }}</p>
        </div>
    </div>
</div>

    <div class="row">
        <div class="col-md-6">
            <div>{{ bar_chart|safe }}</div>
        </div>
        <div class="col-md-6">
            <div>{{ pie_chart|safe }}</div>
        </div>
    </div>
    <div class="row mt-4">
        <div class="col-md-6">
            <div>{{ funnel_chart|safe }}</div>
        </div>
        <div class="col-md-6">
            <div>{{ china_map|safe }}</div>
        </div>
    </div>

</div>

{% endblock %}