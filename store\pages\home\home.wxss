/* pages/home/<USER>/page {
  background-color: rgb(255, 255, 255);
}
.swiper {

  height: 350rpx;
  width: 100%;


  
}
.swiper image {
  height: 100%;
  width: 100%;



}


@keyframes around {
  from {
   margin-left: 100%;
  }
  to {
   /* var接受传入的变量 */
   margin-left: var(--marqueeWidth--);
  }
  }
 .marquee_container{
  background-color: #f30e5a;
  height: 50rpx;
  line-height: 44rpx;
  position: relative;
  width: 100%;

 }
 .marquee_container:hover{
  /* 不起作用 */
  animation-play-state: paused;
 }
 .marquee_text{
  color:rgb(255, 255, 255);
  font-style: 楷体;
  font-size: 28rpx;
  display: inline-block;
  white-space: nowrap;
  animation-name: around;
  animation-duration: 10s; /*过渡时间*/
  animation-iteration-count: infinite;
  animation-timing-function:linear;
 }
 .circle-btn {
  margin-top: 120px;
  width: 200px;
  height: 200px;
  background-color: #42b983;
  border-radius: 50%;
  animation: pulse 2s infinite;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 40px;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}



/* 搜索栏和筛选框样式 */
.container {
  padding: 5px;
}
.search-bar {
  width: 96%;
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  align-items: center;
 
}

.search-bar input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-right: 10px;
}

.search-bar button {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}


.category-list {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 5px;
 
}





.container1 {
  width: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 2%;
  padding: 30rpx;


}


.item-container{
  margin-top: 20rpx;
  display: flex;
  justify-content: space-around;
  flex-direction: row;
  text-align: center;
  flex-wrap: wrap; 

}

.item {
  text-align: center;
  padding: 10rpx;
  margin-top: 10rpx;
}

.item-text{
  margin-top: 5rpx;
  padding: 10rpx;
  color: #afb8b4;
}

.item-image {
  width: 150rpx;
  height: 150rpx;
  object-fit: cover; 
  border-radius: 5rpx ;
  padding: 0 10rpx;
}



/* pages/index/index.wxss */
.container {
  padding: 20rpx;
}

.section {
  margin: 0 10rpx;
  margin-bottom: 40rpx;

}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
 
}

.more-link {
  font-size: 26rpx;
  color: #666;
}

.book-list {
  display: flex;
  justify-content: space-between;
}

.book-item {
  width: 32%;
  background: #fff;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.book-cover {
  width: 100%;
  height: 280rpx;
}

.book-info {
  padding: 15rpx;
}

.book-title {
  display: block;
  font-size: 26rpx;
  color: rgb(2, 0, 0);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.book-author {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin: 5rpx 0;
}

.book-price {
  display: block;
  font-size: 24rpx;
  color: #e4393c;
  font-weight: bold;
}