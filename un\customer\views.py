

from django.middleware.csrf import get_token
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth import authenticate, login
from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken

class LoginView(APIView):
    def post(self, request):
        # 如果用户已经登录，直接返回用户名
        if request.user.is_authenticated:
            response_data = {
                'code': 1,
                'msg': '你已登录，无需重新登录',
                'data': {'username': request.user.username}
            }
            return Response(response_data, status=200)

        # 获取用户名和密码
        username = request.data.get('username')
        password = request.data.get('password')

        # 使用 authenticate 进行用户名密码验证
        user = authenticate(request, username=username, password=password)

        if user is None:
            # 用户名或密码错误
            return Response({'code': 0, 'msg': '用户认证失败'}, status=400)

        # 用户登录
        login(request, user)

        refresh = RefreshToken.for_user(user)
        data = {
            "username": user.username,
            'access_token': str(refresh.access_token),
            'refresh_token': str(refresh)
        }

        # print(data)
        return Response({'code': 1, 'msg': '登录成功', 'data': data}, status=200)


from rest_framework.views import APIView
from rest_framework.response import Response
from django.contrib.auth.models import User
from django.contrib.auth.hashers import make_password
from rest_framework_simplejwt.tokens import RefreshToken

class RegisterView(APIView):
    def post(self, request):
        username = request.data.get('username')
        password = request.data.get('password')

        if User.objects.filter(username=username).exists():
            return Response({'code': 0, 'msg': '用户名已存在'}, status=400)

  
        user = User.objects.create(
            username=username,
            password=make_password(password)
        )
        profie=Profile.objects.create(user=user)
        return Response({'code': 1, 'msg': '注册成功'})





from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.middleware.csrf import get_token
from .models import Profile 
from rest_framework.exceptions import NotFound
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import NotFound, ValidationError
from .models import Profile
from .serializers import ProfileSerializer  

from rest_framework.parsers import MultiPartParser

class AvatarUploadView(APIView):
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser]

    def post(self, request):
        user = request.user
        avatar_file = request.FILES.get('avatar')
        
        if not avatar_file:
            raise ValidationError(detail="请上传头像文件")
        
        try:
            profile = Profile.objects.get(user=user)
        except Profile.DoesNotExist:
            raise NotFound(detail="用户资料不存在")
        
        # 保存新头像
        profile.avatar = avatar_file
        profile.save()
        
        return Response({
            'code': 1,
            'msg': '头像上传成功',
            'avatar_url': profile.avatar.url
        }, status=200)

class UserInfoView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # 获取当前用户
        user = request.user

        # 获取用户资料
        try:
            profile = Profile.objects.get(user=user)
        except Profile.DoesNotExist:
            raise NotFound(detail="用户资料不存在")

        serializer = ProfileSerializer(profile, context={'request': request})
        print(serializer.data)
        return Response({'code': 1, 'msg': '获取成功', 'data': serializer.data}, status=200)

    def post(self, request):
        user = request.user

        try:
            profile = Profile.objects.get(user=user)
        except Profile.DoesNotExist:
            raise NotFound(detail="用户资料不存在")

        serializer = ProfileSerializer(
            profile, 
            data=request.data, 
            partial=True,
            context={'request': request}
        )

        if serializer.is_valid():
            serializer.save()
            return Response({
                'code': 1, 
                'msg': '资料更新成功', 
                'data': serializer.data
            }, status=200)
        else:
            return Response({
                'code': 0,
                'msg': '验证失败',
                'errors': serializer.errors
            }, status=400)
    


class RechargeView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user
        amount = request.data.get('amount', 0)
        profile = Profile.objects.get(user=user)
        profile.moneys += int(amount)
        profile.save()
        
        return Response({'code': 1, 'msg': '充值成功', 'data': {'moneys': profile.moneys}}, status=200)


from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.pagination import PageNumberPagination
from .models import Book, Profile
from django.db.models import Q

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from .models import Book
from .serializers import BookSerializer
from django.db.models import Q

class StandardPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

    def get_paginated_response(self, data):
        return Response({
            'code': 1,
            'msg': 'success',
            'data': {
                'count': self.page.paginator.count,
                'next': self.get_next_link(),
                'previous': self.get_previous_link(),
                'results': data
            }
        })

class BookListView(APIView):
    pagination_class = StandardPagination()

    def get(self, request):
        try:
            queryset = Book.objects.all().order_by('id')
            
            # 搜索功能
            query = request.GET.get('query')
            if query:
                queryset = queryset.filter(
                    Q(name__icontains=query) | 
                    Q(author__icontains=query) |
                    Q(cbs__icontains=query)
                )

            # 分页
            page = self.pagination_class.paginate_queryset(queryset, request)
            serializer = BookSerializer(page, many=True)
            
            return self.pagination_class.get_paginated_response(serializer.data)
            
        except Exception as e:
            return Response({
                'code': 0,
                'msg': str(e),  # 返回具体错误信息
                'data': None
            }, status=500)
        


from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from .models import Book, Bookcomment
from .serializers import BookDetailSerializer
from django.shortcuts import get_object_or_404

from .serializers import BookDetailSerializer, BookCommentSerializer 

class BookDetailView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request, id):
        try:
            book = get_object_or_404(Book, id=id)
            book_serializer = BookDetailSerializer(book)
            comments = Bookcomment.objects.filter(book=book)
            comment_serializer = BookCommentSerializer(comments, many=True)
            
            response_data = {
                'code': 1,
                'msg': 'success',
                'data': {
                    'book': book_serializer.data,
                    'comments': comment_serializer.data  
                }
            }
            return Response(response_data)
            
        except Exception as e:
            return Response({
                'code': 0,
                'msg': f'获取图书详情失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        


from django.shortcuts import get_object_or_404
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from .models import Book, Cart, Favorite
from .serializers import BookDetailSerializer

class FavoriteView(APIView):
    permission_classes = [IsAuthenticated]
    
    def post(self, request, book_id):
        try:
            book = get_object_or_404(Book, id=book_id)
            favorite, created = Favorite.objects.get_or_create(
                user=request.user,
                product=book
            )
            
            if not created:
                favorite.delete()
                return Response({
                    'code': 1,
                    'msg': '已取消收藏',
                    'data': {'is_favorited': False}
                })
                
            return Response({
                'code': 1,
                'msg': '收藏成功',
                'data': {'is_favorited': True}
            })
            
        except Exception as e:
            print(e)
            return Response({
                'code': 0,
                'msg': f'操作失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from .models import Cart, Book, Order
import json

class CartView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        book_id = request.data.get('book_id')
        count = request.data.get('count', 1)

        try:
            book = Book.objects.get(id=book_id)
        except Book.DoesNotExist:
            return Response({
                "code": 0, 
                "msg": "图书不存在",
                "data": None
            }, status=status.HTTP_404_NOT_FOUND)

        # 检查用户是否已经在已支付订单中购买过该图书
        paid_orders = Order.objects.filter(
            user=request.user, 
            status=1  # 已支付订单
        )
        
        for order in paid_orders:
            try:
                goods = json.loads(order.goods)
                if not isinstance(goods, list):
                    continue
                
                # 检查当前图书是否在订单中
                for item in goods:
                    if str(item.get('product_id')) == str(book.id):
                        return Response({
                            "code": 0,
                            "msg": "您已购买过该图书，无需重复购买",
                            "data": None
                        })
            except json.JSONDecodeError:
                continue

        # 如果未购买过，则添加到购物车
        cart_item, created = Cart.objects.get_or_create(
            user=request.user, 
            product=book
        )
        
        if not created:
            cart_item.count += count
        else:
            cart_item.count = 1

        cart_item.save()

        return Response({
            "code": 1, 
            "msg": "添加成功",
            "data": None
        })

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .models import Cart, Book, Order, Profile
import json
from decimal import Decimal

class CartListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        cart_items = Cart.objects.filter(user=user).select_related('product')
        cart_data = []
        total_price = Decimal('0.00')
        
        for item in cart_items:
            item_total = Decimal(item.product.price) * item.count
            cart_data.append({
                'id': item.id,
                'product_id': item.product.id,
                'product_name': item.product.name,
                'product_price': str(item.product.price),
                'product_image': item.product.img_url,
                'quantity': item.count,
                'item_total': str(item_total)
            })
            total_price += item_total
            
        return Response({
            "code": 1, 
            "msg": "获取成功", 
            "data": {
                "items": cart_data,
                "total_price": str(total_price),
                "total_items": len(cart_data)
            }
        })

class CartUpdateView(APIView):
    permission_classes = [IsAuthenticated]

    def patch(self, request, cart_id):
        user = request.user
        quantity = request.data.get('quantity')

        if quantity is None or quantity <= 0:
            return Response({"code": 0, "msg": "数量必须大于零"})

        try:
            cart_item = Cart.objects.get(id=cart_id, user=user)
            # 检查库存
            if cart_item.product.stock < quantity:
                return Response({
                    "code": 0,
                    "msg": f"库存不足，当前库存为{cart_item.product.stock}"
                })
                
            cart_item.count = quantity
            cart_item.save()
            return Response({"code": 1, "msg": "数量更新成功"})
        except Cart.DoesNotExist:
            return Response({"code": 0, "msg": "商品不在购物车中"})

class CartDeleteView(APIView):
    permission_classes = [IsAuthenticated]
    
    def post(self, request, cart_id):
        user = request.user
        try:
            cart_item = Cart.objects.get(id=cart_id, user=user)
            cart_item.delete()
            return Response({"code": 1, "msg": "删除成功"})
        except Cart.DoesNotExist:
            return Response({"code": 0, "msg": "商品不在购物车中"})

class PlaceOrderView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user
        cart_items = Cart.objects.filter(user=user).select_related('product')

        if not cart_items:
            return Response({"code": 0, "msg": "购物车为空"})

        # 检查库存并计算总价
        total_price = Decimal('0.00')
        order_goods = []
        for item in cart_items:
            item_total = Decimal(item.product.price) * item.count
            total_price += item_total
            order_goods.append({
                'product_id': item.product.id,
                'product_name': item.product.name,
                'product_image': item.product.img_url,
                'product_price': str(item.product.price),
                'quantity': item.count,
                'item_total': str(item_total)
            })

        # 检查用户余额
        try:
            profile = Profile.objects.get(user=user)
            if profile.moneys < total_price:
                return Response({
                    "code": 0, 
                    "msg": "余额不足",
                    "data": {
                        "required": str(total_price),
                        "balance": str(profile.moneys)
                    }
                })
        except Profile.DoesNotExist:
            return Response({"code": 0, "msg": "用户信息不完整"})
        # 扣减余额
        profile.moneys -= total_price
        profile.save()

        # 创建订单
        order = Order.objects.create(
            user=user,
            goods=json.dumps(order_goods, ensure_ascii=False),
            total=str(total_price),
            status=1  # 1表示已支付
        )

        # 清空购物车
        cart_items.delete()

        return Response({
            "code": 1, 
            "msg": "下单成功", 
            "data": {
                "order_id": order.id,
                "total_price": str(total_price),
                "created_at": order.create_time.strftime('%Y-%m-%d %H:%M:%S')
            }
        })



class OrderListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        orders = Order.objects.filter(user=user)
        orders_data = []
        for order in orders:
            orders_data.append({
                'order_num': order.order_num,
                'total': order.total,
                'status': order.status,
                'create_time': order.create_time.strftime('%Y-%m-%d %H:%M:%S')
            })
        return Response({"code": 1, "msg": "获取成功", "data": orders_data})

class OrderDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, order_num):
        try:
            order = Order.objects.get(user=request.user, order_num=order_num)
            order_data = {
                'order_num': order.order_num,
                'total': order.total,
                'status': order.status,
                'goods': json.loads(order.goods),
                'create_time': order.create_time.strftime('%Y-%m-%d %H:%M:%S')
            }
            return Response({"code": 1, "msg": "获取成功", "data": order_data})
        except Order.DoesNotExist:
            return Response({"code": 0, "msg": "订单不存在"})


from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .models import Order, Book
import json

class MybooksView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        user = request.user
        paid_orders = Order.objects.filter(user=user, status=1)
        
        books_data = []
        book_ids = set()
        
        for order in paid_orders:
            try:
                goods = json.loads(order.goods)
                print(goods)
                if not isinstance(goods, list):
                    continue
                
                for item in goods:
                    # 确保有 product_id 字段
                    if 'product_id' not in item:
                        continue
                    
                    book_id = item['product_id']
                    
                    # 去重检查
                    if book_id in book_ids:
                        continue
                
                    book_ids.add(book_id)
                    
                    try:
                        book = Book.objects.get(id=book_id)
                        books_data.append({
                            'book_id': book.id,
                            'name': book.name,
                            'author': book.author,
                            'image': book.img_url,
                            'price': float(book.price),
                            'publisher': book.cbs,
                            'publish_year': book.nf,
                            'description': book.description or book.des or '',
                            'category': book.category.name if book.category else ''
                        })
                    except Book.DoesNotExist:
                        continue
                        
            except json.JSONDecodeError:
                continue
        
        return Response({
            "code": 1,
            "msg": "获取成功",
            "data": books_data
        })


from rest_framework.response import Response
from rest_framework import status
from .models import Comment
from .serializers import CommentSerializer

class FeedbackView(APIView):
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """
        创建新的用户反馈
        """
        serializer = CommentSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response({
                'code': 200,
                'message': '反馈提交成功',
                'data': serializer.data
            }, status=status.HTTP_201_CREATED)
        return Response({
            'code': 400,
            'message': '提交失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    def get(self, request):
        comments = Comment.objects.filter(user=request.user)
        serializer = CommentSerializer(comments, many=True)
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': serializer.data
        })


from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .models import Book
import random

class BookmarkView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        # 获取所有图书ID列表
        all_books = Book.objects.all()
        if not all_books.exists():
            return Response({
                'code': 404,
                'message': '暂无图书数据'
            }, status=404)
        
        # 随机选择6本图书
        all_book_ids = list(all_books.values_list('id', flat=True))
        selected_ids = random.sample(all_book_ids, min(len(all_book_ids), 6))
        selected_books = Book.objects.filter(id__in=selected_ids)
        
        # 将6本图书分成热门和推荐两组
        books_data = []
        for book in selected_books:
            books_data.append({
                'id': book.id,
                'name': book.name,
                'author': book.author,
                'img_url': book.img_url,
                'price': str(book.price),
                'category': book.category.name if book.category else ''
            })
    
        random.shuffle(books_data)
        hot_books = books_data[:3]
        recommend_books = books_data[3:6]
        
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': {
                'hot_books': hot_books,
                'recommend_books': recommend_books
            }
        })

from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from .models import Category, Book
from .serializers import CategorySerializer, Book1Serializer

class BookCategoryView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        # 获取所有分类
        categories = Category.objects.all()
        category_serializer = CategorySerializer(categories, many=True)
        
        category_id = request.query_params.get('category_id', None)
        if category_id:
            books = Book.objects.filter(category_id=category_id)
        else:
            books = Book.objects.all()
        
        book_serializer = Book1Serializer(books, many=True)
        
        return Response({
            'categories': category_serializer.data,
            'books': book_serializer.data
        })


from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from .models import Book, Content
from decimal import Decimal

class BookReadView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request, book_id):
        try:
            book = Book.objects.get(id=book_id)
            contents = book.contents.all().order_by('id')
            
            # 获取当前阅读的章节ID（从查询参数获取）
            current_content_id = request.query_params.get('content_id')
            current_content = None
            
            if current_content_id:
                current_content = contents.filter(id=current_content_id).first()
            else:
                current_content = contents.first()
            
            # 序列化数据
            book_data = {
                'id': book.id,
                'name': book.name,
                'author': book.author,
                'cover': book.img_url
            }
            
            contents_data = [{
                'id': c.id,
                'title': c.title
            } for c in contents]
            
            current_content_data = {
                'id': current_content.id,
                'title': current_content.title,
                'content': current_content.content
            } if current_content else None
            
            return Response({
                'book': book_data,
                'contents': contents_data,
                'current_content': current_content_data
            })
            
        except Book.DoesNotExist:
            return Response({'error': '图书不存在'}, status=404)



