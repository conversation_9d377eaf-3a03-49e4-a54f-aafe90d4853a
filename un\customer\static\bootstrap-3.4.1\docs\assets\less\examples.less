// stylelint-disable selector-max-id, selector-no-qualifying-type

// Grid examples
//
// Highlight the grid columns within the docs so folks can see their padding,
// alignment, sizing, etc.

.show-grid {
  margin-bottom: 15px;
}
.show-grid [class^="col-"] {
  padding-top: 10px;
  padding-bottom: 10px;
  background-color: #eee;
  background-color: rgba(86, 61, 124, .15);
  border: 1px solid #ddd;
  border: 1px solid rgba(86, 61, 124, .2);
}

// Examples
//
// Isolated sections of example content for each component or feature. Usually
// followed by a code snippet.

.bs-example {
  position: relative;
  padding: 45px 15px 15px;
  margin: 0 -15px 15px;
  border-color: #e5e5e5 #eee #eee;
  border-style: solid;
  border-width: 1px 0;
  box-shadow: inset 0 3px 6px rgba(0, 0, 0, .05);
}
// Echo out a label for the example
.bs-example:after {
  position: absolute;
  top: 15px;
  left: 15px;
  font-size: 12px;
  font-weight: 700;
  color: #959595;
  text-transform: uppercase;
  letter-spacing: 1px;
  content: "Example";
}

.bs-example-padded-bottom {
  padding-bottom: 24px;
}

// Tweak display of the code snippets when following an example
.bs-example + .highlight,
.bs-example + .bs-clipboard + .highlight {
  margin: -15px -15px 15px;
  border-width: 0 0 1px;
  border-radius: 0;
}

// Make the examples and snippets not full-width
@media (min-width: @screen-sm-min) {
  .bs-example {
    margin-right: 0;
    margin-left: 0;
    background-color: #fff;
    border-color: #ddd;
    border-width: 1px;
    border-radius: 4px 4px 0 0;
    box-shadow: none;
  }
  .bs-example + .highlight,
  .bs-example + .bs-clipboard + .highlight {
    margin-top: -16px;
    margin-right: 0;
    margin-left: 0;
    border-width: 1px;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
  }
  .bs-example + .bs-clipboard .btn-clipboard {
    top: -15px; // due to padding .bs-example has
    border-top-right-radius: 0;
  }
  .bs-example-standalone {
    border-radius: 4px;
  }
}

// Undo width of container
.bs-example .container {
  width: auto;
}

// Tweak content of examples for optimum awesome
.bs-example > p:last-child,
.bs-example > ul:last-child,
.bs-example > ol:last-child,
.bs-example > blockquote:last-child,
.bs-example > .form-control:last-child,
.bs-example > .table:last-child,
.bs-example > .navbar:last-child,
.bs-example > .jumbotron:last-child,
.bs-example > .alert:last-child,
.bs-example > .panel:last-child,
.bs-example > .list-group:last-child,
.bs-example > .well:last-child,
.bs-example > .progress:last-child,
.bs-example > .table-responsive:last-child > .table {
  margin-bottom: 0;
}
.bs-example > p > .close {
  float: none;
}

// Typography
.bs-example-type .table .type-info {
  color: #767676;
  vertical-align: middle;
}
.bs-example-type .table td {
  padding: 15px 0;
  border-color: #eee;
}
.bs-example-type .table tr:first-child td {
  border-top: 0;
}
.bs-example-type h1,
.bs-example-type h2,
.bs-example-type h3,
.bs-example-type h4,
.bs-example-type h5,
.bs-example-type h6 {
  margin: 0;
}

// Contextual background colors
.bs-example-bg-classes p {
  padding: 15px;
}

// Images
.bs-example > .img-circle,
.bs-example > .img-rounded,
.bs-example > .img-thumbnail {
  margin: 5px;
}

// Tables
.bs-example > .table-responsive > .table {
  background-color: #fff;
}

// Buttons
.bs-example > .btn,
.bs-example > .btn-group {
  margin-top: 5px;
  margin-bottom: 5px;
}
.bs-example > .btn-toolbar + .btn-toolbar {
  margin-top: 10px;
}

// Forms
.bs-example-control-sizing {
  select,
  input[type="text"] + input[type="text"] {
    margin-top: 10px;
  }
}
.bs-example-form .input-group {
  margin-bottom: 10px;
}
.bs-example > textarea.form-control {
  resize: vertical;
}

// List groups
.bs-example > .list-group {
  max-width: 400px;
}

// Navbars
.bs-example .navbar:last-child {
  margin-bottom: 0;
}
.bs-navbar-top-example,
.bs-navbar-bottom-example {
  z-index: 1;
  padding: 0;
  overflow: hidden; // cut the drop shadows off
}
.bs-navbar-top-example .navbar-header,
.bs-navbar-bottom-example .navbar-header {
  margin-left: 0;
}
.bs-navbar-top-example .navbar-fixed-top,
.bs-navbar-bottom-example .navbar-fixed-bottom {
  position: relative;
  margin-right: 0;
  margin-left: 0;
}
.bs-navbar-top-example {
  padding-bottom: 45px;
}
.bs-navbar-top-example:after {
  top: auto;
  bottom: 15px;
}
.bs-navbar-top-example .navbar-fixed-top {
  top: -1px;
}
.bs-navbar-bottom-example {
  padding-top: 45px;
}
.bs-navbar-bottom-example .navbar-fixed-bottom {
  bottom: -1px;
}
.bs-navbar-bottom-example .navbar {
  margin-bottom: 0;
}
@media (min-width: 768px) {
  .bs-navbar-top-example .navbar-fixed-top,
  .bs-navbar-bottom-example .navbar-fixed-bottom {
    position: absolute;
  }
}

// Pagination
.bs-example .pagination {
  margin-top: 10px;
  margin-bottom: 10px;
}

// Pager
.bs-example > .pager {
  margin-top: 0;
}

// Example modals
.bs-example-modal {
  background-color: #f5f5f5;
}
.bs-example-modal .modal {
  position: relative;
  top: auto;
  right: auto;
  bottom: auto;
  left: auto;
  z-index: 1;
  display: block;
}
.bs-example-modal .modal-dialog {
  left: auto;
  margin-right: auto;
  margin-left: auto;
}

// Example dropdowns
.bs-example > .dropdown > .dropdown-toggle {
  float: left;
}
.bs-example > .dropdown > .dropdown-menu {
  position: static;
  display: block;
  margin-bottom: 5px;
  clear: left;
}

// Example tabbable tabs
.bs-example-tabs .nav-tabs {
  margin-bottom: 15px;
}

// Tooltips
.bs-example-tooltips {
  text-align: center;
}
.bs-example-tooltips > .btn {
  margin-top: 5px;
  margin-bottom: 5px;
}
.bs-example-tooltip .tooltip {
  position: relative;
  display: inline-block;
  margin: 10px 20px;
  opacity: 1;
}

// Popovers
.bs-example-popover {
  padding-bottom: 24px;
  background-color: #f9f9f9;
}
.bs-example-popover .popover {
  position: relative;
  display: block;
  float: left;
  width: 260px;
  margin: 20px;
}

// Scrollspy demo on fixed height div
.scrollspy-example {
  position: relative;
  height: 200px;
  margin-top: 10px;
  overflow: auto;
}

.bs-example > .nav-pills-stacked-example {
  max-width: 300px;
}

// Simple collapse example
#collapseExample .well {
  margin-bottom: 0;
}

// Pseudo :focus state for showing how it looks in the docs
#focusedInput {
  border-color: rgb(204, 204, 204); // Restate unfocused value to make CSSLint happy that there's a pre-CSS3 fallback
  border-color: rgba(82, 168, 236, .8);
  outline: 0;
  outline: thin dotted \9; // IE6-9
  box-shadow: 0 0 8px rgba(82, 168, 236, .6);
}
