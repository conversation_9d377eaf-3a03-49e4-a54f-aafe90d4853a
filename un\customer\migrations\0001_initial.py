# Generated by Django 4.2.20 on 2025-06-10 12:07

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Book',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bid', models.CharField(blank=True, max_length=100, null=True, unique=True, verbose_name='图书编号')),
                ('name', models.CharField(max_length=30, verbose_name='书名')),
                ('author', models.CharField(max_length=30, verbose_name='作者')),
                ('img_url', models.CharField(max_length=300, verbose_name='图片')),
                ('biy', models.TextField(blank=True, max_length=200, null=True, verbose_name='标语')),
                ('description', models.TextField(blank=True, null=True, verbose_name='图书简介')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='图书价格')),
                ('nf', models.CharField(max_length=30, verbose_name='出版年份')),
                ('cbs', models.CharField(max_length=30, verbose_name='出版社')),
                ('des', models.TextField(blank=True, max_length=500, null=True, verbose_name='简介')),
                ('kcl', models.IntegerField(default=10, verbose_name='库存量')),
                ('content', models.TextField(blank=True, null=True, verbose_name='内容')),
            ],
            options={
                'verbose_name': '图书',
                'verbose_name_plural': '图书',
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='分类名称')),
            ],
        ),
        migrations.CreateModel(
            name='Profile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('moneys', models.IntegerField(default=0, verbose_name='余额')),
                ('isvip', models.IntegerField(default=0, verbose_name='是否为会员')),
                ('sex', models.IntegerField(choices=[(0, '男'), (1, '女')], default=0, verbose_name='性别')),
                ('phone', models.CharField(default='未知', max_length=20, verbose_name='手机号')),
                ('address', models.CharField(default='未知', max_length=100, verbose_name='地址')),
                ('avatar', models.ImageField(default='avatar/default.jpg', upload_to='avatar', verbose_name='头像')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.IntegerField(choices=[(0, '未支付'), (1, '已支付')], default=0, verbose_name='订单状态')),
                ('goods', models.TextField(default='{}', verbose_name='图书')),
                ('total', models.FloatField(default=0, verbose_name='图书总价')),
                ('order_num', models.CharField(max_length=128, verbose_name='订单号')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '订单',
                'verbose_name_plural': '订单',
            },
        ),
        migrations.CreateModel(
            name='Favorite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer.book', verbose_name='图书')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '收藏',
                'verbose_name_plural': '收藏',
            },
        ),
        migrations.CreateModel(
            name='Content',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.TextField(verbose_name='标题')),
                ('content', models.TextField(blank=True, null=True, verbose_name='内容')),
                ('book', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contents', to='customer.book', verbose_name='图书')),
            ],
            options={
                'verbose_name': '内容',
                'verbose_name_plural': '内容',
            },
        ),
        migrations.CreateModel(
            name='Comment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.CharField(blank=True, max_length=128, null=True, verbose_name='联系方式')),
                ('lx', models.CharField(choices=[('bug', '错误报告'), ('suggestion', '功能建议'), ('question', '问题咨询'), ('other', '其他反馈')], default='other', max_length=128, verbose_name='反馈类型')),
                ('cont', models.TextField(default='', verbose_name='反馈内容')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('reply', models.TextField(blank=True, null=True, verbose_name='回复内容')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户反馈',
                'verbose_name_plural': '用户反馈',
                'ordering': ['-create_time'],
            },
        ),
        migrations.CreateModel(
            name='Cart',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('count', models.IntegerField(default=1, verbose_name='图书数量')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer.book', verbose_name='图书')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '购物车',
                'verbose_name_plural': '购物车',
            },
        ),
        migrations.CreateModel(
            name='Bookcomment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(blank=True, null=True, verbose_name='评论内容')),
                ('book', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='customer.book', verbose_name='图书')),
            ],
            options={
                'verbose_name': '书评',
            },
        ),
        migrations.AddField(
            model_name='book',
            name='category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='customer.category', verbose_name='图书分类'),
        ),
    ]
