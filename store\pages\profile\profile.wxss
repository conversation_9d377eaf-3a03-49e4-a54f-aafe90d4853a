/* pages/profile/profile.wxss */
.container {
  padding: 20rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.label {
  width: 160rpx;
  font-size: 30rpx;
  color: #333;
}

.value {
  flex: 1;
  font-size: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}

.radio-label {
  margin-right: 40rpx;
}

.picker {
  padding: 20rpx 0;
}

.form-submit {
  margin-top: 60rpx;
  padding: 0 30rpx;
}

/* 自定义picker样式 */
picker {
  width: 100%;
}