#!/usr/bin/env python3
"""
检查数据库中的数据
"""
import os
import sys
import django

# 设置Django环境
sys.path.append('un')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'un.settings')
django.setup()

from customer.models import Book, User, Bookcomment
from django.contrib.auth import get_user_model

def check_data():
    print("=== 数据库数据检查 ===")
    
    # 检查图书数据
    books = Book.objects.all()
    print(f"图书总数: {books.count()}")
    
    if books.exists():
        first_book = books.first()
        print(f"第一本书: {first_book.name} (ID: {first_book.id})")
        
        # 检查该书的评论
        comments = Bookcomment.objects.filter(book=first_book)
        print(f"该书评论数: {comments.count()}")
        
        if comments.exists():
            for comment in comments[:3]:  # 显示前3条评论
                user_name = comment.user.username if comment.user else "匿名"
                print(f"  - {user_name}: {comment.content[:50]}...")
    
    # 检查用户数据
    User = get_user_model()
    users = User.objects.all()
    print(f"用户总数: {users.count()}")
    
    if users.exists():
        first_user = users.first()
        print(f"第一个用户: {first_user.username}")
    
    print("\n=== 检查完成 ===")

if __name__ == "__main__":
    check_data()
