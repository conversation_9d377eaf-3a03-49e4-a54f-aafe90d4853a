"""
URL configuration for un project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.conf import settings
from django.conf.urls.static import static

from django.contrib import admin
from django.urls import path
from customer import views
from customer import adm
urlpatterns = [
    path('admin/', admin.site.urls),
    path('login/', views.LoginView.as_view(), name='login'),
    path('register/', views.RegisterView.as_view(), name='register'),
    path('recharge/', views.RechargeView.as_view(), name='recharge'),
    path('books/', views.BookListView.as_view(), name='book-list'),
    path('books/<str:id>/', views.BookDetailView.as_view(), name='book-detail'),
    path('books/<int:book_id>/comments/', views.BookCommentView.as_view(), name='book-comment'),
    path('favorites/<int:book_id>/', views.FavoriteView.as_view(), name='favorite'),
    path('cart/', views.CartView.as_view(), name='cart'),

    path('cart_list/', views.CartListView.as_view(), name='cart-list'),
    path('cart/<int:cart_id>/update/', views.CartUpdateView.as_view(), name='cart-update'),
    path('cart/<int:cart_id>/delete/', views.CartDeleteView.as_view(), name='cart-delete'),
    path('orders/place/', views.PlaceOrderView.as_view(), name='place-order'),

    path('order_list/', views.OrderListView.as_view(), name='order-list'),
    path('order_detail/<str:order_num>/', views.OrderDetailView.as_view(), name='order-detail'),
    path('api/mybooks/', views.MybooksView.as_view(), name='mybooks'),
    path('feedback/', views.FeedbackView.as_view(), name='user-feedback'),

    path('api/bookmark/', views.BookmarkView.as_view(), name='bookmark'),

    path('api/books/categories/', views.BookCategoryView.as_view(), name='book-categories'),
    path('api/books/<int:book_id>/read/', views.BookReadView.as_view(), name='book-read'),
    path('api/userinfo/', views.UserInfoView.as_view(), name='user-info'),
    path('api/upload-avatar/', views.AvatarUploadView.as_view(), name='upload-avatar'),

    path('', adm.alogin),
    path('alogin/', adm.alogin, name='alogin'),
    path('ahome/', adm.ahome, name='ahome'),
    path('books/<int:pk>/delete/',  adm.book_delete, name='book_delete'),
    path('books/<int:pk>/edit/', adm.BookEditView.as_view(), name='book_edit'),
    path('abooks/<int:pk>/',  adm.abook_detail, name='abook_detail'),
    path('add-book/', adm.add_book, name='add_book'),
    path('book/<int:book_id>/content/<int:content_id>/',  adm.acontent_detail, name='acontent_detail'),

     path('aorder_list/',  adm.aorder_list, name='aorder_list'),
    path('aorder_list<int:order_id>/detail/',  adm.aorder_detail, name='aorder_detail'),
    path('aorder_list/<int:order_id>/delete/', adm.adelete_order, name='adelete_order'),


    path('afeedback/', adm.afeed_back, name='afeed_back'),
    path('afeedback/edit_reply/<int:comment_id>/', adm.edit_reply, name='edit_reply'),
    path('afeedback/delete_reply/<int:comment_id>/', adm.delete_reply, name='delete_reply'),
    path('data/', adm.a_data, name='a_data'),
    path('alogout/', adm.alogout, name='alogout'),

] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
