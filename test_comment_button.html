<!DOCTYPE html>
<html>
<head>
    <title>测试评论按钮逻辑</title>
    <style>
        .container { padding: 20px; max-width: 400px; margin: 0 auto; }
        .comment-input { width: 100%; height: 100px; padding: 10px; margin-bottom: 10px; }
        .submit-btn { width: 100%; padding: 10px; }
        .submit-btn:disabled { background-color: #ccc; color: #999; }
        .submit-btn:not(:disabled) { background-color: #1aad19; color: white; }
        .status { margin-top: 10px; padding: 10px; background: #f5f5f5; }
    </style>
</head>
<body>
    <div class="container">
        <h2>评论按钮状态测试</h2>
        <textarea 
            id="commentInput" 
            class="comment-input" 
            placeholder="写下您的书评..."
            maxlength="500"
        ></textarea>
        <button id="submitBtn" class="submit-btn" disabled>发表评论</button>
        
        <div class="status">
            <p>输入内容: <span id="inputText">""</span></p>
            <p>去空格后长度: <span id="trimmedLength">0</span></p>
            <p>按钮状态: <span id="buttonStatus">禁用</span></p>
        </div>
    </div>

    <script>
        const commentInput = document.getElementById('commentInput');
        const submitBtn = document.getElementById('submitBtn');
        const inputText = document.getElementById('inputText');
        const trimmedLength = document.getElementById('trimmedLength');
        const buttonStatus = document.getElementById('buttonStatus');

        function updateButtonState() {
            const value = commentInput.value;
            const canSubmit = value.trim().length > 0;
            
            // 更新显示
            inputText.textContent = `"${value}"`;
            trimmedLength.textContent = value.trim().length;
            buttonStatus.textContent = canSubmit ? '启用' : '禁用';
            
            // 更新按钮状态
            submitBtn.disabled = !canSubmit;
        }

        commentInput.addEventListener('input', updateButtonState);
        
        submitBtn.addEventListener('click', function() {
            if (!submitBtn.disabled) {
                alert('评论提交成功！');
                commentInput.value = '';
                updateButtonState();
            }
        });

        // 初始化
        updateButtonState();
    </script>
</body>
</html>
