
const app = getApp();
Page({
  data: {

    movies:[  
      {url:'https://p5.ssl.qhimgs1.com/sdr/400__/t03a0e3d6705e6a7ed5.png'} ,  
      {url:'https://p1.ssl.qhimgs1.com/sdr/400__/t0195801bd73f81e291.jpg'} ,  
      {url:'https://p0.ssl.qhimgs1.com/sdr/400__/t019c3f8c2473f11bb4.jpg'} ,  
      {url:'https://p0.ssl.qhimgs1.com/sdr/400__/t04d8c57dff8c0d7f54.jpg'}   
      ],
      hotBooks: [],
      recommendBooks: []


  },
  onLoad() {
    this.loadBookData();
  },

  loadBookData() {
    wx.showLoading({
      title: '加载中...',
    });

    wx.request({
      url: 'http://127.0.0.1:8000/api/bookmark/',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success: (res) => {
        if (res.statusCode === 200) {
          this.setData({
            hotBooks: res.data.data.hot_books || [],
            recommendBooks: res.data.data.recommend_books || []
          });
        }
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  onPullDownRefresh() {
    this.loadBookData(() => {
      wx.stopPullDownRefresh();
    });
  },
  navigateToMore(){
    wx.navigateTo({
      url: '/pages/book/book',
    })
  },
    // 跳转详情页
    navigateToDetail(e) {
      const bookId = e.currentTarget.dataset.id;
      console.log(bookId);

      wx.navigateTo({
        url: `/pages/bookDetail/bookDetail?id=${bookId}`
      })
    }

});
