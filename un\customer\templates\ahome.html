{% extends 'base.html' %}
{% block content %}
{% load static %}


<style>

    
    .container1 {
        max-width: 1400px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    h1 {
        text-align: center;
        color: #2c3e50;
        margin-bottom: 30px;
    }
    
    .filters {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 5px;
    }
    
    .filter-group {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    label {
        font-weight: bold;
        color: #495057;
    }
    
    input, select {
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
    }
    
    button {
        padding: 8px 16px;
        background-color: #3498db;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;
    }
    
    button:hover {
        background-color: #2980b9;
    }
    
    .reset-btn {
        background-color: #95a5a6;
    }
    .reset-btn {
        background-color: #b62cd1;
    }
    
    .reset-btn:hover {
        background-color: #7f8c8d;
    }
    
    table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
    }
    
    th, td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }
    
    th {
        background-color: #3498db;
        color: white;
        font-weight: bold;
    }
    
    tr:nth-child(even) {
        background-color: #f2f2f2;
    }
    
    tr:hover {
        background-color: #e3f2fd;
    }
    
    .book-img {
        width: 50px;
        height: auto;
        border-radius: 3px;
    }
    
    .no-books {
        text-align: center;
        padding: 20px;
        color: #7f8c8d;
        font-style: italic;
    }

    .pagination {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            padding: 10px 0;
        }
        
        .pagination a, .pagination span {
            color: #3498db;
            padding: 8px 16px;
            text-decoration: none;
            border: 1px solid #ddd;
            margin: 0 4px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .pagination a:hover {
            background-color: #e3f2fd;
        }
        
        .pagination .current {
            background-color: #3498db;
            color: white;
            border: 1px solid #3498db;
        }
        
        .pagination .disabled {
            color: #95a5a6;
            pointer-events: none;
            cursor: default;
        }

        .actions {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.btn-view, .btn-edit, .btn-delete {
    padding: 5px 10px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 12px;
    transition: all 0.3s;
    white-space: nowrap;
}

.btn-view {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}

.btn-view:hover {
    background-color: #2980b9;
}

.btn-edit {
    background-color: #2ecc71;
    color: white;
    border: 1px solid #27ae60;
}

.btn-edit:hover {
    background-color: #27ae60;
}

.btn-delete {
    background-color: #e74c3c;
    color: white;
    border: 1px solid #c0392b;
}

.btn-delete:hover {
    background-color: #c0392b;
}
</style>

<div class="container1" style="margin-top: 80px;">
    <form method="get" class="filters">
        <div class="filter-group">
            <label for="search">搜索:</label>
            <input type="text" id="search" name="search" value="{{ search_query }}" placeholder="书名/作者/出版社">
        </div>
        
        <div class="filter-group">
            <label for="category">分类:</label>
            <select id="category" name="category">
                <option value="">所有分类</option>
                {% for category in categories %}
                    <option value="{{ category.id }}" {% if selected_category == category.id|stringformat:"s" %}selected{% endif %}>
                        {{ category.name }}
                    </option>
                {% endfor %}
            </select>
        </div>
        
        <div class="filter-group">
            <label for="year">年份:</label>
            <input type="text" id="year" name="year" value="{{ selected_year }}" placeholder="出版年份">
        </div>
        
        <button type="submit">筛选</button>
        <a href="{% url 'ahome' %}"><button type="button" class="reset-btn">重置</button></a>
        <a href="{% url 'add_book' %}"><button type="button" class="add-btn">添加图书</button></a>
    </form>
    
    <!-- 图书表格 -->
    {% if books %}
        <table>
            <thead>
                <tr>
                    <th>封面</th>
                    <th>书名</th>
                    <th>作者</th>
                    <th>出版社</th>
                    <th>出版年份</th>
                    <th>价格</th>
                    <th>分类</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for book in books %}
                    <tr>
                        <td><img src="{{ book.img_url }}" alt="{{ book.name }}" class="book-img"></td>
                        <td>{{ book.name }}</td>
                        <td>{{ book.author }}</td>
                        <td>{{ book.cbs }}</td>
                        <td>{{ book.nf }}</td>
                        <td>¥{{ book.price }}</td>
                        <td>{{ book.category.name }}</td>
                        <td>
                            <a href="{% url 'abook_detail' book.id %}" class="btn-view">查看</a>
                            <a href="{% url 'book_edit' book.id %}?page={{ books.number }}" class="btn-edit">编辑</a>
                            <a href="{% url 'book_delete' book.id %}?page={{ books.number }}" class="btn-delete">删除</a>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
        <div class="pagination">
            <span class="step-links">
                {% if books.has_previous %}
                    <a href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_year %}&year={{ selected_year }}{% endif %}">&laquo; 首页</a>
                    <a href="?page={{ books.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_year %}&year={{ selected_year }}{% endif %}">上一页</a>
                {% else %}
                    <span class="disabled">&laquo; 首页</span>
                    <span class="disabled">上一页</span>
                {% endif %}
                
                <span class="current">
                    第 {{ books.number }} 页 / 共 {{ books.paginator.num_pages }} 页
                </span>
                
                {% if books.has_next %}
                    <a href="?page={{ books.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_year %}&year={{ selected_year }}{% endif %}">下一页</a>
                    <a href="?page={{ books.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_year %}&year={{ selected_year }}{% endif %}">末页 &raquo;</a>
                {% else %}
                    <span class="disabled">下一页</span>
                    <span class="disabled">末页 &raquo;</span>
                {% endif %}
            </span>
        </div>
    {% else %}
        <div class="no-books">
            <p>没有找到符合条件的图书</p>
        </div>
    {% endif %}
</div>

{% endblock %}