# Generated by Django 4.2.20 on 2025-06-16 12:10

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('customer', '0003_alter_bookcomment_options_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='bookcomment',
            options={'ordering': ['-create_time'], 'verbose_name': '书评', 'verbose_name_plural': '书评'},
        ),
        migrations.AddField(
            model_name='bookcomment',
            name='create_time',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='创建时间'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='bookcomment',
            name='user',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户'),
            preserve_default=False,
        ),
    ]
