/* 页面容器 */
.container {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 输入框样式 */
input {
  width: 80%;
  height: 100rpx;
  padding: 0 30rpx;
  margin-bottom: 40rpx;
  border-radius: 50rpx;
  border: 2rpx solid #eaeaea;
  background-color: #fff;
  font-size: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

/* 输入框聚焦状态 */
input:focus {
  border-color: #07c160;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.2);
  outline: none;
}

/* 输入框占位符样式 */
input::placeholder {
  color: #ccc;
  font-size: 28rpx;
}

/* 按钮基础样式 */
button {
  width: 80%;
  height: 100rpx;
  border-radius: 50rpx;
  background: linear-gradient(135deg, #07c160, #05a14e);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 按钮点击效果 */
button:active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 按钮禁用状态 */
button[disabled] {
  background: #ccc;
  box-shadow: none;
  color: #fff;
}

/* 金额提示文本 */
.amount-tips {
  margin-top: 30rpx;
  font-size: 24rpx;
  color: #999;
  text-align: center;
}