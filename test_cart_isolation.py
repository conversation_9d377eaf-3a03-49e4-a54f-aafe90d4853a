#!/usr/bin/env python3
"""
测试购物车用户隔离
"""
import requests
import json

def test_cart_isolation():
    """测试不同用户的购物车是否隔离"""
    
    base_url = "http://127.0.0.1:8000"
    
    print("=== 测试购物车用户隔离 ===")
    
    # 测试用户1
    user1_data = {"username": "root0", "password": "123456"}
    user2_data = {"username": "test_user", "password": "test_password"}  # 需要确保这个用户存在
    
    # 1. 用户1登录
    print("1. 用户1 (root0) 登录...")
    user1_login = requests.post(f"{base_url}/login/", json=user1_data)
    if user1_login.status_code == 200 and user1_login.json().get('code') == 1:
        user1_token = user1_login.json()['data']['access_token']
        print(f"✅ 用户1登录成功，token: {user1_token[:20]}...")
        
        # 2. 用户1查看购物车
        print("\n2. 用户1查看购物车...")
        user1_headers = {'Authorization': f'Bearer {user1_token}', 'Content-Type': 'application/json'}
        user1_cart = requests.get(f"{base_url}/cart_list/", headers=user1_headers)
        if user1_cart.status_code == 200:
            user1_cart_data = user1_cart.json()
            print(f"用户1购物车商品数: {user1_cart_data['data']['total_items']}")
            print(f"用户1购物车商品: {[item['product_name'] for item in user1_cart_data['data']['items']]}")
        
        # 3. 用户1添加商品到购物车
        print("\n3. 用户1添加商品到购物车...")
        add_result = requests.post(f"{base_url}/cart/", 
                                 json={"book_id": 1, "count": 1}, 
                                 headers=user1_headers)
        if add_result.status_code == 200:
            print(f"用户1添加商品结果: {add_result.json()['msg']}")
        
        # 4. 用户1再次查看购物车
        print("\n4. 用户1添加后查看购物车...")
        user1_cart_after = requests.get(f"{base_url}/cart_list/", headers=user1_headers)
        if user1_cart_after.status_code == 200:
            user1_cart_after_data = user1_cart_after.json()
            print(f"用户1购物车商品数: {user1_cart_after_data['data']['total_items']}")
            user1_cart_ids = [item['id'] for item in user1_cart_after_data['data']['items']]
            print(f"用户1购物车ID列表: {user1_cart_ids}")
    
    else:
        print("❌ 用户1登录失败")
        return
    
    # 5. 尝试用户2登录（如果用户2不存在，我们创建一个测试）
    print("\n5. 尝试用户2登录...")
    user2_login = requests.post(f"{base_url}/login/", json=user2_data)
    
    if user2_login.status_code == 200 and user2_login.json().get('code') == 1:
        user2_token = user2_login.json()['data']['access_token']
        print(f"✅ 用户2登录成功，token: {user2_token[:20]}...")
        
        # 6. 用户2查看购物车
        print("\n6. 用户2查看购物车...")
        user2_headers = {'Authorization': f'Bearer {user2_token}', 'Content-Type': 'application/json'}
        user2_cart = requests.get(f"{base_url}/cart_list/", headers=user2_headers)
        if user2_cart.status_code == 200:
            user2_cart_data = user2_cart.json()
            print(f"用户2购物车商品数: {user2_cart_data['data']['total_items']}")
            user2_cart_ids = [item['id'] for item in user2_cart_data['data']['items']]
            print(f"用户2购物车ID列表: {user2_cart_ids}")
            
            # 7. 比较两个用户的购物车
            print(f"\n7. 购物车隔离检查:")
            print(f"用户1购物车ID: {user1_cart_ids}")
            print(f"用户2购物车ID: {user2_cart_ids}")
            
            if set(user1_cart_ids).intersection(set(user2_cart_ids)):
                print("❌ 发现购物车ID重叠，用户隔离失败！")
            else:
                print("✅ 购物车ID完全不同，用户隔离正常")
        
    else:
        print("❌ 用户2登录失败，可能用户不存在")
        print("让我们检查数据库中的用户...")
        
        # 检查数据库中的用户
        print("\n检查现有用户...")
        # 这里我们可以通过其他方式检查

def check_database_users():
    """检查数据库中的用户"""
    import os
    import sys
    import django
    
    # 设置Django环境
    sys.path.append('un')
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'un.settings')
    django.setup()
    
    from django.contrib.auth import get_user_model
    from customer.models import Cart
    
    User = get_user_model()
    users = User.objects.all()
    
    print(f"\n=== 数据库用户检查 ===")
    print(f"总用户数: {users.count()}")
    
    for user in users:
        cart_count = Cart.objects.filter(user=user).count()
        print(f"用户: {user.username} (ID: {user.id}) - 购物车商品数: {cart_count}")
        
        # 显示购物车详情
        cart_items = Cart.objects.filter(user=user)
        for item in cart_items:
            print(f"  - 购物车ID: {item.id}, 商品: {item.product.name}, 数量: {item.count}")

if __name__ == "__main__":
    test_cart_isolation()
    check_database_users()
