// pages/profile/profile.js
const app = getApp()

// 中国32个省份列表
const provinces = [
  '未知','北京市', '天津市', '河北省', '山西省', '内蒙古自治区',
  '辽宁省', '吉林省', '黑龙江省', '上海市', '江苏省',
  '浙江省', '安徽省', '福建省', '江西省', '山东省',
  '河南省', '湖北省', '湖南省', '广东省', '广西壮族自治区',
  '海南省', '重庆市', '四川省', '贵州省', '云南省',
  '西藏自治区', '陕西省', '甘肃省', '青海省', '宁夏回族自治区',
  '新疆维吾尔自治区', '台湾省'
];

Page({
  data: {
    userInfo: {
      username: '',
      moneys: 0,
      isvip: 0,
      sex: 0,
      phone: '',
      province: '',
      avatar_url: ''
    },
    sexOptions: [
      { label: '男', value: 0 },
      { label: '女', value: 1 }
    ],
    provinces: provinces,
    provinceIndex: 0,
    tempAvatarPath: null  // 临时头像路径
  },

  onLoad: function() {
    this.loadUserInfo();
  },

  loadUserInfo: function() {
    wx.showLoading({ title: '加载中...' });
    
    wx.request({
      url: 'http://127.0.0.1:8000/api/userinfo/',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success: function(res) {
        wx.hideLoading();
        if (res.data.code === 1) {
          var userInfo = res.data.data;
          var provinceIndex = 0;
          if (userInfo.province) {
            provinceIndex = this.data.provinces.indexOf(userInfo.province);
            if (provinceIndex === -1) provinceIndex = 0;
          }
          this.setData({
            userInfo: userInfo,
            provinceIndex: provinceIndex
          });
        } else {
          wx.showToast({ title: res.data.msg, icon: 'none' });
        }
      }.bind(this),
      fail: function(err) {
        wx.hideLoading();
        wx.showToast({ title: '网络错误', icon: 'none' });
      }
    });
  },

  // 省份选择变化
  bindProvinceChange: function(e) {
    var index = e.detail.value;
    this.setData({
      provinceIndex: index,
      'userInfo.province': this.data.provinces[index]
    });
  },

  chooseAvatar: function() {
    var self = this;
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function(res) {
        var tempFilePath = res.tempFilePaths[0];
        // 只更新本地显示，不立即上传
        self.setData({
          'userInfo.avatar_url': tempFilePath,
          tempAvatarPath: tempFilePath  // 保存临时路径用于后续上传
        });
        console.log('头像已选择，临时路径:', tempFilePath);
      }
    });
  },



  onSexChange: function(e) {
    this.setData({
      'userInfo.sex': parseInt(e.detail.value)
    });
  },

  onPhoneChange: function(e) {
    this.setData({
      'userInfo.phone': e.detail.value
    });
  },

  // 提交表单
  submitForm: function() {
    var self = this;
    var userInfo = this.data.userInfo;

    if (!userInfo.phone) {
      wx.showToast({ title: '请输入手机号', icon: 'none' });
      return;
    }

    wx.showLoading({ title: '保存中...' });

    // 如果有新选择的头像，先上传头像
    if (this.data.tempAvatarPath) {
      console.log('检测到新头像，先上传头像...');
      this.uploadAvatarThenSubmit();
    } else {
      // 没有新头像，直接提交其他信息
      console.log('没有新头像，直接提交其他信息...');
      this.submitUserInfo();
    }
  },

  // 先上传头像，然后提交其他信息
  uploadAvatarThenSubmit: function() {
    var self = this;

    wx.uploadFile({
      url: 'http://127.0.0.1:8000/api/userinfo/',
      filePath: this.data.tempAvatarPath,
      name: 'avatar',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken
      },
      success: function(res) {
        console.log('头像上传响应:', res);
        var data = JSON.parse(res.data);

        if (data.code === 1) {
          console.log('头像上传成功，新URL:', data.avatar_url);
          // 头像上传成功后，继续提交其他信息
          self.submitUserInfo();
        } else {
          wx.hideLoading();
          wx.showToast({
            title: data.msg || '头像上传失败',
            icon: 'none'
          });
        }
      },
      fail: function(err) {
        wx.hideLoading();
        console.error('头像上传失败:', err);
        wx.showToast({
          title: '头像上传失败',
          icon: 'none'
        });
      }
    });
  },

  // 提交用户信息
  submitUserInfo: function() {
    var self = this;
    var userInfo = this.data.userInfo;

    wx.request({
      url: 'http://127.0.0.1:8000/api/userinfo/',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      data: {
        sex: userInfo.sex,
        phone: userInfo.phone,
        province: userInfo.province
      },
      success: function(res) {
        wx.hideLoading();
        if (res.data.code === 1) {
          wx.showToast({ title: '保存成功', icon: 'success' });

          // 通知上一个页面刷新数据
          var pages = getCurrentPages();
          if (pages.length > 1) {
            var prevPage = pages[pages.length - 2];
            if (prevPage.route === 'pages/my/my' && prevPage.get_profile) {
              console.log('通知我的页面刷新数据');
              prevPage.get_profile();
            }
          }

          setTimeout(function() {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({ title: res.data.msg, icon: 'none' });
        }
      },
      fail: function() {
        wx.hideLoading();
        wx.showToast({ title: '网络错误', icon: 'none' });
      }
    });
  }
});