// pages/profile/profile.js
const app = getApp()

// 中国32个省份列表
const provinces = [
  '未知','北京市', '天津市', '河北省', '山西省', '内蒙古自治区',
  '辽宁省', '吉林省', '黑龙江省', '上海市', '江苏省',
  '浙江省', '安徽省', '福建省', '江西省', '山东省',
  '河南省', '湖北省', '湖南省', '广东省', '广西壮族自治区',
  '海南省', '重庆市', '四川省', '贵州省', '云南省',
  '西藏自治区', '陕西省', '甘肃省', '青海省', '宁夏回族自治区',
  '新疆维吾尔自治区', '台湾省'
];

Page({
  data: {
    userInfo: {
      username: '',
      moneys: 0,
      isvip: 0,
      sex: 0,
      phone: '',
      address: '',
      avatar_url: ''
    },
    sexOptions: [
      { label: '男', value: 0 },
      { label: '女', value: 1 }
    ],
    provinces: provinces,
    provinceIndex: 0
  },

  onLoad() {
    this.loadUserInfo();
  },

  loadUserInfo() {
    wx.showLoading({ title: '加载中...' });
    
    wx.request({
      url: 'http://127.0.0.1:8000/api/userinfo/',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data.code === 1) {
          const userInfo = res.data.data;
          let provinceIndex = 0;
          if (userInfo.address) {
            provinceIndex = this.data.provinces.indexOf(userInfo.address);
            if (provinceIndex === -1) provinceIndex = 0;
          }
          this.setData({ 
            userInfo,
            provinceIndex
          });
        } else {
          wx.showToast({ title: res.data.msg, icon: 'none' });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({ title: '网络错误', icon: 'none' });
      }
    });
  },

  // 省份选择变化
  bindProvinceChange(e) {
    const index = e.detail.value;
    this.setData({
      provinceIndex: index,
      'userInfo.address': this.data.provinces[index]
    });
  },

  chooseAvatar: function() {
    var self = this;
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function(res) {
        var tempFilePath = res.tempFilePaths[0];
        // 立即上传头像
        self.uploadAvatar(tempFilePath);
      }
    });
  },

  // 上传头像
  uploadAvatar: function(tempFilePath) {
    var self = this;
    wx.showLoading({
      title: '上传中...',
    });

    wx.uploadFile({
      url: 'http://127.0.0.1:8000/api/userinfo/',
      filePath: tempFilePath,
      name: 'avatar',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken
      },
      success: function(res) {
        wx.hideLoading();
        console.log('头像上传响应:', res);
        var data = JSON.parse(res.data);
        console.log('解析后的数据:', data);

        if (data.code === 1) {
          console.log('头像上传成功，新URL:', data.avatar_url);
          wx.showToast({
            title: '头像更新成功',
            icon: 'success'
          });

          // 添加时间戳强制刷新图片缓存
          var newAvatarUrl = data.avatar_url;
          if (newAvatarUrl && newAvatarUrl.indexOf('?') === -1) {
            newAvatarUrl += '?t=' + new Date().getTime();
          } else if (newAvatarUrl) {
            newAvatarUrl += '&t=' + new Date().getTime();
          }

          console.log('带时间戳的头像URL:', newAvatarUrl);

          // 更新本地显示
          self.setData({
            'userInfo.avatar_url': newAvatarUrl
          });

          console.log('setData后的头像URL:', self.data.userInfo.avatar_url);

          // 通知上一个页面刷新数据
          var pages = getCurrentPages();
          if (pages.length > 1) {
            var prevPage = pages[pages.length - 2];
            if (prevPage.route === 'pages/my/my' && prevPage.get_profile) {
              prevPage.get_profile();
            }
          }
        } else {
          wx.showToast({
            title: data.msg || '上传失败',
            icon: 'none'
          });
        }
      },
      fail: function(err) {
        wx.hideLoading();
        console.error('上传头像失败:', err);
        wx.showToast({
          title: '上传失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  onSexChange(e) {
    this.setData({
      'userInfo.sex': parseInt(e.detail.value)
    });
  },

  onPhoneChange(e) {
    this.setData({
      'userInfo.phone': e.detail.value
    });
  },

  // 提交表单
  submitForm() {
    const { userInfo } = this.data;
    
    if (!userInfo.phone) {
      wx.showToast({ title: '请输入手机号', icon: 'none' });
      return;
    }
    
    wx.showLoading({ title: '提交中...' });
    
    wx.request({
      url: 'http://127.0.0.1:8000/api/userinfo/',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      data: {
        sex: userInfo.sex,
        phone: userInfo.phone,
        address: userInfo.address
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data.code === 1) {
          wx.showToast({ title: '更新成功', icon: 'success' });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({ title: res.data.msg, icon: 'none' });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({ title: '网络错误', icon: 'none' });
      }
    });
  }
});