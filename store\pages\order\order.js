const app = getApp();
Page({
  data: {
    orders: []
  },

  fetchOrders: function () {
    wx.request({
      url: 'http://127.0.0.1:8000/order_list/',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success: (res) => {
        if (res.data.code === 1) {
          this.setData({
            orders: res.data.data
          });
        } else {
          wx.showToast({
            title: '加载失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  onOrderClick: function (event) {
    const orderNum = event.currentTarget.dataset.ordernum;
    wx.navigateTo({
      url: `/pages/order_detail/order_detail?order_num=${orderNum}`
    });
  },

  onShow: function () {
    this.fetchOrders();
  },
});
