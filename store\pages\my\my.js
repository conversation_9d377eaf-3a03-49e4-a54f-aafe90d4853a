// pages/mine/mine.js
var app = getApp()

Page({
  data: {
    userInfo: {
      username: '加载中...',
      moneys: 0,
      isvip: 0,
      avatar_url: '/images/default-avatar.png' // 默认头像
    }
  },

  onLoad: function() {
    this.get_profile();
  },

  onShow: function() {
    // 页面显示时刷新用户信息
    this.get_profile();
  },

  // 获取用户信息
  get_profile: function() {
    wx.showLoading({
      title: '加载中...',
    });
    
    wx.request({
      url: 'http://127.0.0.1:8000/api/userinfo/', 
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success: function(res) {
        wx.hideLoading();
        if (res.data.code === 1) {
          this.setData({
            userInfo: {
              ...res.data.data,
              // 如果没有头像URL，使用默认头像
              avatar_url: res.data.data.avatar_url || '/images/default-avatar.png'
            }
          });
        } else {
          wx.showToast({
            title: res.data.msg || '获取信息失败',
            icon: 'none'
          });
        }
      }.bind(this),
      fail: function(err) {
        wx.hideLoading();
        console.error('获取用户信息失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 选择图片并上传
  chooseImage: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function(res) {
        var tempFilePath = res.tempFilePaths[0];
        this.uploadAvatar(tempFilePath);
      }.bind(this),
      fail: function(err) {
        console.error('选择图片失败:', err);
      }
    });
  },

  // 上传头像
  uploadAvatar: function(tempFilePath) {
    wx.showLoading({
      title: '上传中...',
    });
    
    wx.uploadFile({
      url: 'http://127.0.0.1:8000/api/userinfo/',
      filePath: tempFilePath,
      name: 'avatar',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken
      },
      success: function(res) {
        wx.hideLoading();
        var data = JSON.parse(res.data);
        if (data.code === 1) {
          wx.showToast({
            title: '头像更新成功',
            icon: 'success'
          });
          // 更新本地用户信息
          this.get_profile();
        } else {
          wx.showToast({
            title: data.msg || '上传失败',
            icon: 'none'
          });
        }
      }.bind(this),
      fail: function(err) {
        wx.hideLoading();
        console.error('上传头像失败:', err);
        wx.showToast({
          title: '上传失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 余额充值
  recharge: function() {
    wx.navigateTo({
      url: '/pages/recharge/recharge',
    });
  },

  // 订单中心
  order: function() {
    wx.navigateTo({
      url: '/pages/order/order',
    });
  },

  // 我的图书
  mybook: function() {
    wx.navigateTo({
      url: '/pages/mybook/mybook',
    });
  },

  // 反馈中心
  feedback: function() {
    wx.navigateTo({
      url: '/pages/feedback/feedback',
    });
  },

  // 用户类型点击
  profile: function() {
    wx.navigateTo({
      url: '/pages/profile/profile',
    });
  }
})