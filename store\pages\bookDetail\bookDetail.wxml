<view class="container">
  <!-- 图书基本信息 -->
  <view class="book-header">
    <image src="{{book.book.img_url || img_url}}" mode="aspectFit" class="book-cover"></image>
    <view class="book-info">
      <text class="title">{{book.book.name || name}}</text>
      <text class="price">¥{{book.book.price || price}}</text>
      <text class="author">{{book.book.author || author}} </text>
      <text class="stock">出版社: {{book.book.publisher}}</text>
      <text class="stock">分类: {{book.book.category || category}}</text>
      <view class="meta">
        <text>出版年份: {{book.book.nf || nf}}</text>
      </view>
    </view>
  </view>
  
  <view class="footer">
    <button class="btn borrow" bindtap="addToCart">加购</button>
  </view>

  <!-- 选项卡 -->
  <view class="tabs">
    <view 
      class="tab {{activeTab === 'description' ? 'active' : ''}}" 
      bindtap="switchTab" 
      data-tab="description"
    >
      图书简介
    </view>

    <view 
      class="tab {{activeTab === 'comments' ? 'active' : ''}}" 
      bindtap="switchTab" 
      data-tab="comments"
      style="margin-left: 60rpx;"
    >
      书评({{book.comments.length}})
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content">
    <view wx:if="{{activeTab === 'description'}}" class="description">
      <rich-text nodes="{{book.book.description}}"></rich-text>
    </view>
    <view wx:if="{{activeTab === 'content'}}" class="content-detail">
      <rich-text nodes="{{book.book.content}}"></rich-text>
    </view>
    <view wx:if="{{activeTab === 'comments'}}" class="comments-container">
      <!-- 添加评论输入框 -->
      <view class="add-comment-section">
        <textarea
          class="comment-input"
          placeholder="写下您的书评..."
          value="{{commentText}}"
          bindinput="onCommentInput"
          maxlength="500"
        ></textarea>
        <button class="submit-comment-btn" bindtap="submitComment" disabled="{{!commentText.trim()}}">
          发表评论
        </button>
      </view>

      <!-- 评论列表 -->
      <view class="comments-list">
        <block wx:for="{{book.comments}}" wx:key="id">
          <view class="comment-card">
            <view class="comment-header">
              <text class="comment-user">{{item.username || '匿名用户'}}</text>
              <text class="comment-time">{{item.create_time}}</text>
            </view>
            <view class="comment-content">{{item.content}}</view>
          </view>
        </block>
        <view wx:if="{{book.comments.length === 0}}" class="empty-comments">
          暂无书评，快来发表第一条评论吧~
        </view>
      </view>
    </view>
  </view>
</view>