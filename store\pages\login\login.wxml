<view class="container">
<view class="ctn">
<view class="title">电子书城</view>
  <view class="input-group">

    <input bindinput="onUsernameInput" placeholder="请输入用户名" />
  </view>
  <view class="input-group">
    <input bindinput="onPasswordInput" type="password" placeholder="请输入密码" />
  </view>
  <button bindtap="onLogin" class="login-btn">登录</button>
  <button bindtap="res" class="res-btn">注册</button>
</view>
</view>
