<view class="container">
  <view wx:if="{{books.length > 0}}">
    <block wx:for="{{books}}" wx:key="book_id" >
      <view class="book-item" bindtap="navigateToDetail" data-id="{{item.book_id}}" style="width: 100%;">
        <image src="{{item.image}}" class="book-image"></image>
        <view class="book-info">
          <text class="book-title">{{item.name}}</text>
          <text class="book-author">{{item.author}}</text>
          <text class="book-meta">{{item.publisher}} </text>
          <text class="book-meta">{{item.publish_year}}</text>
          <!-- <text class="book-price">¥{{item.price}}</text> -->
        </view>
      </view>
    </block>
  </view>
  <view wx:else class="empty">
    <text>您还没有购买过任何图书</text>
  </view>
</view>