{"compileType": "miniprogram", "libVersion": "3.5.5", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": false, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false, "ignoreUploadUnusedFiles": true}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}}