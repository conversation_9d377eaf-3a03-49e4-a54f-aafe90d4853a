/* Define general styles */
.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f2f2f2;
  padding: 20px;
}

/* Style the title */
.title {
  margin-top: -20%;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 30px;
  color: #333;
}

/* Style the input group */
.input-group {
  width: 80%;
  margin-bottom: 20px;
}

.input-group view {
  margin-bottom: 5px;
  color: #666;
  font-size: 16px;
}

input {
  width: 90%;
  padding: 12px;
  border-radius: 5px;
  border: 1px solid #ddd;
  background-color: #fff;
  font-size: 16px;
}

input::placeholder {
  color: #aaa;
}

/* Style the login button */
.login-btn {
  width: 80%;
  border: none;
  border-radius: 5px;
  background-color: #007bff;
  color: white;
  font-size: 18px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.login-btn:hover {
  background-color: #0056b3;
}

.res-btn{
  margin-top: 20rpx;
  width: 80%;
  border: none;
  border-radius: 5px;
  background-color: #ca29eb;
  color: white;
  font-size: 18px;
}