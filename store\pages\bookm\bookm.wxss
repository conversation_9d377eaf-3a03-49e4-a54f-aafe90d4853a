/* pages/books/books.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 分类导航样式 */
.category-nav {
  white-space: nowrap;
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.category-item {
  display: inline-block;
  padding: 12rpx 32rpx;
  margin: 0 10rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 40rpx;
  transition: all 0.3s ease;
}

.category-item.active {
  background-color: #6190e8;
  color: white;
  font-weight: 500;
  box-shadow: 0 4rpx 8rpx rgba(97, 144, 232, 0.3);
}

/* 图书列表样式 */
.book-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.book-list.single-book {
  display: block;
}

.book-item {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.book-item:active {
  transform: scale(0.98);
}

.book-cover {
  width: 100%;
  height: 380rpx;
  display: block;
}

.book-list.single-book .book-cover {
  width: 100%;
  height: auto;
  max-height: 600rpx;
}

.book-info {
  padding: 20rpx;
}

.book-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.book-author {
  font-size: 24rpx;
  color: #888;
  display: block;
  margin-bottom: 8rpx;
}

.book-price {
  font-size: 28rpx;
  color: #e74c3c;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.book-category {
  font-size: 22rpx;
  color: #6190e8;
  background-color: #f0f6ff;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  display: inline-block;
}