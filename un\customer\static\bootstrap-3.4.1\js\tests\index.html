<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Bootstrap Plugin Test Suite</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- jQuery -->
    <script src="vendor/jquery.min.js"></script>
    <script>
      // Disable jQuery event aliases to ensure we don't accidentally use any of them
      (function () {
        var eventAliases = [
          'blur',
          'focus',
          'focusin',
          'focusout',
          'load',
          'resize',
          'scroll',
          'unload',
          'click',
          'dblclick',
          'mousedown',
          'mouseup',
          'mousemove',
          'mouseover',
          'mouseout',
          'mouseenter',
          'mouseleave',
          'change',
          'select',
          'submit',
          'keydown',
          'keypress',
          'keyup',
          'error',
          'contextmenu',
          'hover',
          'bind',
          'unbind',
          'delegate',
          'undelegate'
        ]
        for (var i = 0, len = eventAliases.length; i < len; i++) {
          var eventAlias = eventAliases[i]
          $.fn[eventAlias] = function () {
            throw new Error('Using the ".' + eventAlias + '()" method is not allowed, so that Bootstrap can be compatible with custom jQuery builds which exclude the "event aliases" module that defines said method. See https://github.com/twbs/bootstrap/blob/master/CONTRIBUTING.md#js')
          }
        }
      })()
    </script>

    <!-- QUnit -->
    <link rel="stylesheet" href="../../node_modules/qunitjs/qunit/qunit.css" media="screen">
    <script src="../../node_modules/qunitjs/qunit/qunit.js"></script>
    <script>
      // See https://github.com/axemclion/grunt-saucelabs#test-result-details-with-qunit
      var log = []
      // Require assert.expect in each test
      QUnit.config.requireExpects = true
      QUnit.done(function (testResults) {
        var tests = []
        for (var i = 0, len = log.length; i < len; i++) {
          var details = log[i]
          tests.push({
            name: details.name,
            result: details.result,
            expected: details.expected,
            actual: details.actual,
            source: details.source
          })
        }
        testResults.tests = tests

        window.global_test_results = testResults
      })

      QUnit.testStart(function (testDetails) {
        $(window).scrollTop(0)
        QUnit.log(function (details) {
          if (!details.result) {
            details.name = testDetails.name
            log.push(details)
          }
        })
      })

      // Cleanup
      QUnit.testDone(function () {
        $('#qunit-fixture').empty()
        $('#modal-test, .modal-backdrop').remove()
      })

      // Display fixture on-screen on iOS to avoid false positives
      if (/iPhone|iPad|iPod/.test(navigator.userAgent)) {
        QUnit.begin(function() {
          $('#qunit-fixture').css({ top: 0, left: 0 })
        })

        QUnit.done(function () {
          $('#qunit-fixture').css({ top: '', left: '' })
        })
      }

      // Disable deprecated global QUnit method aliases in preparation for QUnit v2
      (function () {
        var methodNames = [
          'async',
          'asyncTest',
          'deepEqual',
          'equal',
          'expect',
          'module',
          'notDeepEqual',
          'notEqual',
          'notPropEqual',
          'notStrictEqual',
          'ok',
          'propEqual',
          'push',
          'start',
          'stop',
          'strictEqual',
          'test',
          'throws'
        ];
        for (var i = 0, len = methodNames.length; i < len; i++) {
          var methodName = methodNames[i];
          window[methodName] = undefined;
        }
      })();
    </script>

    <!-- Plugin sources -->
    <script src="../../js/alert.js"></script>
    <script src="../../js/button.js"></script>
    <script src="../../js/carousel.js"></script>
    <script src="../../js/collapse.js"></script>
    <script src="../../js/dropdown.js"></script>
    <script src="../../js/modal.js"></script>
    <script src="../../js/scrollspy.js"></script>
    <script src="../../js/tab.js"></script>
    <script src="../../js/tooltip.js"></script>
    <script src="../../js/popover.js"></script>
    <script src="../../js/affix.js"></script>

    <!-- Unit tests -->
    <script src="unit/alert.js"></script>
    <script src="unit/button.js"></script>
    <script src="unit/carousel.js"></script>
    <script src="unit/collapse.js"></script>
    <script src="unit/dropdown.js"></script>
    <script src="unit/modal.js"></script>
    <script src="unit/scrollspy.js"></script>
    <script src="unit/tab.js"></script>
    <script src="unit/tooltip.js"></script>
    <script src="unit/popover.js"></script>
    <script src="unit/affix.js"></script>

  </head>
  <body>
    <div id="qunit-container">
      <div id="qunit"></div>
      <div id="qunit-fixture"></div>
    </div>
  </body>
</html>
