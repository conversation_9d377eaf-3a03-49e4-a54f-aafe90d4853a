var app = getApp();

Page({
  data: {
    feedbackList: [],
    page: 1,
    pageSize: 10,
    hasMore: true,
    isLoading: false
  },

  onLoad() {
    this.loadFeedbackList();
  },

  // 格式化时间为年月日
  formatDate: function(dateStr) {
    var date = new Date(dateStr);
    var year = date.getFullYear();
    var month = (date.getMonth() + 1).toString();
    var day = date.getDate().toString();

    // 手动补零
    if (month.length === 1) month = '0' + month;
    if (day.length === 1) day = '0' + day;

    return year + '-' + month + '-' + day;
  },

  // 加载反馈列表
  loadFeedbackList: function() {
    if (this.data.isLoading) return;
    
    this.setData({ isLoading: true });
    
    wx.request({
      url: 'http://127.0.0.1:8000/feedback/',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      data: {
        page: this.data.page,
        page_size: this.data.pageSize
      },
      success: function(res) {
        if (res.statusCode === 200) {
          var newList = res.data.data || [];
          var self = this;

          // 格式化反馈列表中的日期
          var formattedList = newList.map(function(item) {
            item.create_time = self.formatDate(item.create_time); // 格式化时间
            return item;
          });

          var combinedList = this.data.page === 1 ? formattedList : this.data.feedbackList.concat(formattedList);
          
          this.setData({
            feedbackList: combinedList,
            hasMore: newList.length >= this.data.pageSize
          });
        }
      }.bind(this),
      complete: function() {
        this.setData({ isLoading: false });
      }.bind(this)
    });
  },

  // 加载更多
  loadMore: function() {
    if (!this.data.hasMore) return;
    
    var self = this;
    this.setData({
      page: this.data.page + 1
    }, function() {
      self.loadFeedbackList();
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    var self = this;
    this.setData({
      page: 1,
      feedbackList: []
    }, function() {
      self.loadFeedbackList(function() {
        wx.stopPullDownRefresh();
      });
    });
  }
});
