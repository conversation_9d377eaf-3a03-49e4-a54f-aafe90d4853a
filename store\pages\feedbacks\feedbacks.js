const app = getApp();

Page({
  data: {
    feedbackList: [],
    page: 1,
    pageSize: 10,
    hasMore: true,
    isLoading: false
  },

  onLoad() {
    this.loadFeedbackList();
  },

  // 格式化时间为年月日
  formatDate(dateStr) {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 加载反馈列表
  loadFeedbackList() {
    if (this.data.isLoading) return;
    
    this.setData({ isLoading: true });
    
    wx.request({
      url: 'http://127.0.0.1:8000/feedback/',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      data: {
        page: this.data.page,
        page_size: this.data.pageSize
      },
      success: (res) => {
        if (res.statusCode === 200) {
          const newList = res.data.data || [];
          
          // 格式化反馈列表中的日期
          const formattedList = newList.map(item => {
            item.create_time = this.formatDate(item.create_time); // 格式化时间
            return item;
          });

          const combinedList = this.data.page === 1 ? formattedList : [...this.data.feedbackList, ...formattedList];
          
          this.setData({
            feedbackList: combinedList,
            hasMore: newList.length >= this.data.pageSize
          });
        }
      },
      complete: () => {
        this.setData({ isLoading: false });
      }
    });
  },

  // 加载更多
  loadMore() {
    if (!this.data.hasMore) return;
    
    this.setData({
      page: this.data.page + 1
    }, () => {
      this.loadFeedbackList();
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({
      page: 1,
      feedbackList: []
    }, () => {
      this.loadFeedbackList(() => {
        wx.stopPullDownRefresh();
      });
    });
  }
});
