<div class="bs-docs-section">
  <h1 id="responsive-utilities" class="page-header">Responsive utilities</h1>

  <p class="lead">For faster mobile-friendly development, use these utility classes for showing and hiding content by device via media query. Also included are utility classes for toggling content when printed.</p>
  <p>Try to use these on a limited basis and avoid creating entirely different versions of the same site. Instead, use them to complement each device's presentation.</p>


  <h2 id="responsive-utilities-classes">Available classes</h2>
  <p>Use a single or combination of the available classes for toggling content across viewport breakpoints.</p>
  <div class="table-responsive">
    <table class="table table-bordered table-striped responsive-utilities">
      <thead>
        <tr>
          <th></th>
          <th>
            Extra small devices
            <small>Phones (&lt;768px)</small>
          </th>
          <th>
            Small devices
            <small>Tablets (&ge;768px)</small>
          </th>
          <th>
            Medium devices
            <small>Desktops (&ge;992px)</small>
          </th>
          <th>
            Large devices
            <small>Desktops (&ge;1200px)</small>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <th scope="row"><code>.visible-xs-*</code></th>
          <td class="is-visible">Visible</td>
          <td class="is-hidden">Hidden</td>
          <td class="is-hidden">Hidden</td>
          <td class="is-hidden">Hidden</td>
        </tr>
        <tr>
          <th scope="row"><code>.visible-sm-*</code></th>
          <td class="is-hidden">Hidden</td>
          <td class="is-visible">Visible</td>
          <td class="is-hidden">Hidden</td>
          <td class="is-hidden">Hidden</td>
        </tr>
        <tr>
          <th scope="row"><code>.visible-md-*</code></th>
          <td class="is-hidden">Hidden</td>
          <td class="is-hidden">Hidden</td>
          <td class="is-visible">Visible</td>
          <td class="is-hidden">Hidden</td>
        </tr>
        <tr>
          <th scope="row"><code>.visible-lg-*</code></th>
          <td class="is-hidden">Hidden</td>
          <td class="is-hidden">Hidden</td>
          <td class="is-hidden">Hidden</td>
          <td class="is-visible">Visible</td>
        </tr>
      </tbody>
      <tbody>
        <tr>
          <th scope="row"><code>.hidden-xs</code></th>
          <td class="is-hidden">Hidden</td>
          <td class="is-visible">Visible</td>
          <td class="is-visible">Visible</td>
          <td class="is-visible">Visible</td>
        </tr>
        <tr>
          <th scope="row"><code>.hidden-sm</code></th>
          <td class="is-visible">Visible</td>
          <td class="is-hidden">Hidden</td>
          <td class="is-visible">Visible</td>
          <td class="is-visible">Visible</td>
        </tr>
        <tr>
          <th scope="row"><code>.hidden-md</code></th>
          <td class="is-visible">Visible</td>
          <td class="is-visible">Visible</td>
          <td class="is-hidden">Hidden</td>
          <td class="is-visible">Visible</td>
        </tr>
        <tr>
          <th scope="row"><code>.hidden-lg</code></th>
          <td class="is-visible">Visible</td>
          <td class="is-visible">Visible</td>
          <td class="is-visible">Visible</td>
          <td class="is-hidden">Hidden</td>
        </tr>
      </tbody>
    </table>
  </div>

  <p>As of v3.2.0, the <code>.visible-*-*</code> classes for each breakpoint come in three variations, one for each CSS <code>display</code> property value listed below.</p>
  <div class="table-responsive">
    <table class="table table-bordered table-striped">
      <thead>
        <tr>
          <th>Group of classes</th>
          <th>CSS <code>display</code></th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <th scope="row"><code>.visible-*-block</code></th>
          <td><code>display: block;</code></td>
        </tr>
        <tr>
          <th scope="row"><code>.visible-*-inline</code></th>
          <td><code>display: inline;</code></td>
        </tr>
        <tr>
          <th scope="row"><code>.visible-*-inline-block</code></th>
          <td><code>display: inline-block;</code></td>
        </tr>
      </tbody>
    </table>
  </div>
  <p>So, for extra small (<code>xs</code>) screens for example, the available <code>.visible-*-*</code> classes are: <code>.visible-xs-block</code>, <code>.visible-xs-inline</code>, and <code>.visible-xs-inline-block</code>.</p>
  <p>The classes <code>.visible-xs</code>, <code>.visible-sm</code>, <code>.visible-md</code>, and <code>.visible-lg</code> also exist, but are <strong>deprecated as of v3.2.0</strong>. They are approximately equivalent to <code>.visible-*-block</code>, except with additional special cases for toggling <code>&lt;table&gt;</code>-related elements.</p>

  <h2 id="responsive-utilities-print">Print classes</h2>
  <p>Similar to the regular responsive classes, use these for toggling content for print.</p>
  <div class="table-responsive">
    <table class="table table-bordered table-striped responsive-utilities">
      <thead>
        <tr>
          <th>Classes</th>
          <th>Browser</th>
          <th>Print</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <th scope="row">
            <code>.visible-print-block</code><br>
            <code>.visible-print-inline</code><br>
            <code>.visible-print-inline-block</code>
          </th>
          <td class="is-hidden">Hidden</td>
          <td class="is-visible">Visible</td>
        </tr>
        <tr>
          <th scope="row"><code>.hidden-print</code></th>
          <td class="is-visible">Visible</td>
          <td class="is-hidden">Hidden</td>
        </tr>
      </tbody>
    </table>
  </div>
  <p>The class <code>.visible-print</code> also exists but is <strong>deprecated</strong> as of v3.2.0. It is approximately equivalent to <code>.visible-print-block</code>, except with additional special cases for <code>&lt;table&gt;</code>-related elements.</p>


  <h2 id="responsive-utilities-tests">Test cases</h2>
  <p>Resize your browser or load on different devices to test the responsive utility classes.</p>

  <h3>Visible on...</h3>
  <p>Green checkmarks indicate the element <strong>is visible</strong> in your current viewport.</p>
  <div class="row responsive-utilities-test visible-on">
    <div class="col-xs-6 col-sm-3">
      <span class="hidden-xs">Extra small</span>
      <span class="visible-xs-block">&#10004; Visible on x-small</span>
    </div>
    <div class="col-xs-6 col-sm-3">
      <span class="hidden-sm">Small</span>
      <span class="visible-sm-block">&#10004; Visible on small</span>
    </div>
    <div class="clearfix visible-xs-block"></div>
    <div class="col-xs-6 col-sm-3">
      <span class="hidden-md">Medium</span>
      <span class="visible-md-block">&#10004; Visible on medium</span>
    </div>
    <div class="col-xs-6 col-sm-3">
      <span class="hidden-lg">Large</span>
      <span class="visible-lg-block">&#10004; Visible on large</span>
    </div>
  </div>
  <div class="row responsive-utilities-test visible-on">
    <div class="col-xs-6">
      <span class="hidden-xs hidden-sm">Extra small and small</span>
      <span class="visible-xs-block visible-sm-block">&#10004; Visible on x-small and small</span>
    </div>
    <div class="col-xs-6">
      <span class="hidden-md hidden-lg">Medium and large</span>
      <span class="visible-md-block visible-lg-block">&#10004; Visible on medium and large</span>
    </div>
    <div class="clearfix visible-xs-block"></div>
    <div class="col-xs-6">
      <span class="hidden-xs hidden-md">Extra small and medium</span>
      <span class="visible-xs-block visible-md-block">&#10004; Visible on x-small and medium</span>
    </div>
    <div class="col-xs-6">
      <span class="hidden-sm hidden-lg">Small and large</span>
      <span class="visible-sm-block visible-lg-block">&#10004; Visible on small and large</span>
    </div>
    <div class="clearfix visible-xs-block"></div>
    <div class="col-xs-6">
      <span class="hidden-xs hidden-lg">Extra small and large</span>
      <span class="visible-xs-block visible-lg-block">&#10004; Visible on x-small and large</span>
    </div>
    <div class="col-xs-6">
      <span class="hidden-sm hidden-md">Small and medium</span>
      <span class="visible-sm-block visible-md-block">&#10004; Visible on small and medium</span>
    </div>
  </div>

  <h3>Hidden on...</h3>
  <p>Here, green checkmarks also indicate the element <strong>is hidden</strong> in your current viewport.</p>
  <div class="row responsive-utilities-test hidden-on">
    <div class="col-xs-6 col-sm-3">
      <span class="hidden-xs">Extra small</span>
      <span class="visible-xs-block">&#10004; Hidden on x-small</span>
    </div>
    <div class="col-xs-6 col-sm-3">
      <span class="hidden-sm">Small</span>
      <span class="visible-sm-block">&#10004; Hidden on small</span>
    </div>
    <div class="clearfix visible-xs-block"></div>
    <div class="col-xs-6 col-sm-3">
      <span class="hidden-md">Medium</span>
      <span class="visible-md-block">&#10004; Hidden on medium</span>
    </div>
    <div class="col-xs-6 col-sm-3">
      <span class="hidden-lg">Large</span>
      <span class="visible-lg-block">&#10004; Hidden on large</span>
    </div>
  </div>
  <div class="row responsive-utilities-test hidden-on">
    <div class="col-xs-6">
      <span class="hidden-xs hidden-sm">Extra small and small</span>
      <span class="visible-xs-block visible-sm-block">&#10004; Hidden on x-small and small</span>
    </div>
    <div class="col-xs-6">
      <span class="hidden-md hidden-lg">Medium and large</span>
      <span class="visible-md-block visible-lg-block">&#10004; Hidden on medium and large</span>
    </div>
    <div class="clearfix visible-xs-block"></div>
    <div class="col-xs-6">
      <span class="hidden-xs hidden-md">Extra small and medium</span>
      <span class="visible-xs-block visible-md-block">&#10004; Hidden on x-small and medium</span>
    </div>
    <div class="col-xs-6">
      <span class="hidden-sm hidden-lg">Small and large</span>
      <span class="visible-sm-block visible-lg-block">&#10004; Hidden on small and large</span>
    </div>
    <div class="clearfix visible-xs-block"></div>
    <div class="col-xs-6">
      <span class="hidden-xs hidden-lg">Extra small and large</span>
      <span class="visible-xs-block visible-lg-block">&#10004; Hidden on x-small and large</span>
    </div>
    <div class="col-xs-6">
      <span class="hidden-sm hidden-md">Small and medium</span>
      <span class="visible-sm-block visible-md-block">&#10004; Hidden on small and medium</span>
    </div>
  </div>
</div>
