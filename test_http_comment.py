#!/usr/bin/env python3
"""
通过HTTP请求测试评论API
"""
import requests
import json

def test_comment_via_http():
    """通过HTTP请求测试评论功能"""
    
    base_url = "http://127.0.0.1:8000"
    
    print("=== HTTP评论API测试 ===")
    
    # 1. 先尝试登录获取token
    print("1. 尝试登录...")
    
    # 使用我们知道存在的用户
    login_data = {
        "username": "root0",
        "password": "123456"  # 常见的测试密码
    }
    
    try:
        login_response = requests.post(f"{base_url}/login/", json=login_data, timeout=10)
        print(f"登录响应状态: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            print(f"登录响应: {login_result}")
            
            if login_result.get('code') == 1:
                token = login_result['data']['access_token']
                print(f"✅ 登录成功，token: {token[:20]}...")
                
                # 2. 测试添加评论
                print("\n2. 测试添加评论...")
                
                headers = {
                    'Authorization': f'Bearer {token}',
                    'Content-Type': 'application/json'
                }
                
                comment_data = {
                    "content": "这是通过HTTP API测试添加的评论"
                }
                
                book_id = 1  # 测试第一本书
                comment_url = f"{base_url}/books/{book_id}/comments/"
                
                print(f"请求URL: {comment_url}")
                print(f"请求头: {headers}")
                print(f"请求数据: {comment_data}")
                
                comment_response = requests.post(
                    comment_url,
                    json=comment_data,
                    headers=headers,
                    timeout=10
                )
                
                print(f"评论响应状态: {comment_response.status_code}")
                print(f"评论响应内容: {comment_response.text}")
                
                if comment_response.status_code == 201:
                    result = comment_response.json()
                    if result.get('code') == 1:
                        print("✅ 评论添加成功！")
                        print(f"返回数据: {result['data']}")
                    else:
                        print(f"❌ 评论添加失败: {result.get('msg')}")
                else:
                    print(f"❌ HTTP请求失败: {comment_response.status_code}")
                    
                # 3. 验证评论是否添加成功
                print("\n3. 验证评论...")
                book_detail_response = requests.get(
                    f"{base_url}/books/{book_id}/",
                    headers=headers,
                    timeout=10
                )
                
                if book_detail_response.status_code == 200:
                    book_data = book_detail_response.json()
                    if book_data.get('code') == 1:
                        comments = book_data['data']['comments']
                        print(f"✅ 该书现有评论数: {len(comments)}")
                        if comments:
                            latest_comment = comments[0]
                            print(f"最新评论: {latest_comment}")
                    else:
                        print(f"❌ 获取图书详情失败: {book_data.get('msg')}")
                else:
                    print(f"❌ 获取图书详情HTTP错误: {book_detail_response.status_code}")
                    
            else:
                print(f"❌ 登录失败: {login_result.get('msg')}")
        else:
            print(f"❌ 登录HTTP错误: {login_response.status_code}")
            print(f"响应内容: {login_response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保Django服务器正在运行")
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_comment_via_http()
