<view class="container">
  <!-- 搜索框 -->
  <view class="search-box">
    <input 
      placeholder="搜索书名/作者/出版社" 
      bindinput="onSearchInput" 
      bindconfirm="onSearchConfirm"
      value="{{searchQuery}}"
    />
    <button bindtap="onSearch">搜索</button>
  </view>

  <!-- 图书列表 -->
  <scroll-view 
    scroll-y 
    style="height: 100vh;" 
    bindscrolltolower="loadMore"
    lower-threshold="50"
  >
    <view wx:if="{{books.length > 0}}">
      <view class="book-item" wx:for="{{books}}" wx:key="id" bindtap="navigateToDetail" data-id="{{item.id}}">
        <image src="{{item.img_url}}" mode="aspectFill"></image>
        <view class="book-info">
          <text class="title">{{item.name}}</text>
          <text class="author">{{item.author}} | {{item.publisher}}</text>
          <text class="price">¥{{item.price}}</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:else class="empty">
      <image src="/images/empty.png" mode="widthFix"></image>
      <text>暂无图书数据</text>
    </view>

    <!-- 加载更多提示 -->
    <view class="loading" wx:if="{{isLoading}}">
      <text>加载中...</text>
    </view>
    <view class="no-more" wx:if="{{!hasMore && books.length > 0}}">
      <text>没有更多数据了</text>
    </view>
  </scroll-view>
</view>