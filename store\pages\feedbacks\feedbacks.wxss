/* 整体容器 */
.container {
  padding: 20rpx;
  background-color: #f8f8f8;
  padding: 0;
  margin: 0;
}

/* 反馈列表 */
.feedback-list {
  margin-top: 20rpx;
  width: 100%;
  
}

/* 反馈卡片 */
.feedback-card {
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  padding: 20rpx;
}

/* 卡片头部 - 反馈类型和日期 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.feedback-type {
  padding: 4rpx 10rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #fff;
}

.feedback-type.positive {
  background-color: #4CAF50;
}

.feedback-type.neutral {
  background-color: #FFC107;
}

.feedback-type.negative {
  background-color: #F44336;
}

.feedback-date {
  font-size: 22rpx;
  color: #888;
}

/* 卡片内容 */
.card-content {
  margin-bottom: 15rpx;
}

.feedback-content {
  font-size: 24rpx;
  color: #333;
}

/* 管理员回复 */
.card-reply {
  margin-top: 15rpx;
  padding: 10rpx;
  border-top: 1rpx solid #e1e1e1;
}

.reply-label {
  font-weight: bold;
  color: #333;
}

.reply-content {
  font-size: 22rpx;
  color: #444;
  margin-top: 10rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #888;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 20rpx 0;
}

.load-more text {
  font-size: 24rpx;
  color: #007BFF;
}
