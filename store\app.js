App({
  globalData: {
    accessToken: '',
    refreshToken: '',
    username: '',
  },

  onLaunch: function () {
    // 当应用启动时，检查是否已保存用户的 access_token
    const accessToken = wx.getStorageSync('access_token');
    const refreshToken = wx.getStorageSync('refresh_token');
    const username = wx.getStorageSync('username');

    if (accessToken && refreshToken && username) {
      this.globalData.accessToken = accessToken;
      this.globalData.refreshToken = refreshToken;
      this.globalData.username = username;
    }
  },

  // 登录函数，封装在 app.js 中
  login: function (username, password, successCallback, failureCallback) {
    wx.request({
      url: 'http://127.0.0.1:8000/login/',  
      method: 'POST',
      data: {
        username,
        password,
      },
      header: {
        'Content-Type': 'application/json',
      },
      success: (res) => {
        if (res.data.code === 1) {
          const { access_token, refresh_token, username } = res.data.data;

          // 将返回的 access_token、refresh_token 和用户名保存到全局数据中
          this.globalData.accessToken = access_token;
          this.globalData.refreshToken = refresh_token;
          this.globalData.username = username;

          // 同时将数据存储到本地缓存
          wx.setStorageSync('access_token', access_token);
          wx.setStorageSync('refresh_token', refresh_token);
          wx.setStorageSync('username', username);

          // 调用成功回调
          if (successCallback) {
            successCallback(res);
          }
        } else {
          // 调用失败回调
          if (failureCallback) {
            failureCallback(res);
          }
        }
      },
      fail: (err) => {
        console.error('登录请求失败:', err);
        if (failureCallback) {
          failureCallback(err);
        }
      }
    });
  }
});
