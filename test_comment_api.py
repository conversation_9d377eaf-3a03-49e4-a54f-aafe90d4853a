#!/usr/bin/env python3
"""
测试图书评论API
"""
import requests
import json

def test_comment_api():
    """测试评论API"""
    
    base_url = "http://127.0.0.1:8000"
    
    print("=== 图书评论API测试 ===")
    
    # 1. 用户登录
    print("1. 用户登录...")
    login_data = {"username": "root0", "password": "123456"}
    login_response = requests.post(f"{base_url}/login/", json=login_data)
    
    if login_response.status_code == 200 and login_response.json().get('code') == 1:
        token = login_response.json()['data']['access_token']
        print(f"✅ 登录成功，token: {token[:20]}...")
        
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        # 2. 获取图书详情（包含评论）
        book_id = 1  # 测试图书ID
        print(f"\n2. 获取图书{book_id}的详情...")
        book_response = requests.get(f"{base_url}/books/{book_id}/", headers=headers)
        
        if book_response.status_code == 200:
            book_data = book_response.json()
            print(f"图书名称: {book_data['data']['book']['name']}")
            print(f"现有评论数: {len(book_data['data']['comments'])}")
            
            # 3. 添加评论
            print(f"\n3. 为图书{book_id}添加评论...")
            comment_data = {
                "content": "这是通过API测试添加的评论，非常好的一本书！"
            }
            
            comment_response = requests.post(
                f"{base_url}/books/{book_id}/comments/", 
                json=comment_data, 
                headers=headers
            )
            
            if comment_response.status_code == 201:
                comment_result = comment_response.json()
                print(f"✅ 评论添加成功！")
                print(f"评论ID: {comment_result['data']['id']}")
                print(f"评论内容: {comment_result['data']['content']}")
                print(f"评论用户: {comment_result['data']['username']}")
                
                # 4. 再次获取图书详情验证评论
                print(f"\n4. 验证评论是否添加成功...")
                verify_response = requests.get(f"{base_url}/books/{book_id}/", headers=headers)
                if verify_response.status_code == 200:
                    verify_data = verify_response.json()
                    new_comment_count = len(verify_data['data']['comments'])
                    print(f"✅ 验证成功，现在有 {new_comment_count} 条评论")
                    
                    # 显示最新评论
                    if verify_data['data']['comments']:
                        latest_comment = verify_data['data']['comments'][-1]
                        print(f"最新评论: {latest_comment}")
                
            else:
                print(f"❌ 评论添加失败: {comment_response.text}")
        else:
            print(f"❌ 获取图书详情失败: {book_response.text}")
    else:
        print("❌ 登录失败")

if __name__ == "__main__":
    test_comment_api()
