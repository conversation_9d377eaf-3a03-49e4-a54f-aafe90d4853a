#!/usr/bin/env python3
"""
测试图书评论API的脚本
"""
import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:8000"

def test_comment_api():
    """测试评论API功能"""
    
    # 1. 首先需要登录获取token
    print("=== 测试评论API ===")
    
    # 测试用户登录
    login_data = {
        "username": "test_user",  # 需要替换为实际的测试用户
        "password": "test_password"
    }
    
    print("1. 尝试登录...")
    login_response = requests.post(f"{BASE_URL}/login/", json=login_data)
    print(f"登录响应状态: {login_response.status_code}")
    
    if login_response.status_code == 200:
        login_result = login_response.json()
        if login_result.get('code') == 1:
            token = login_result['data']['access_token']
            print(f"登录成功，获取到token: {token[:20]}...")
            
            # 2. 测试获取图书详情
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            book_id = "1"  # 测试图书ID
            print(f"\n2. 获取图书 {book_id} 的详情...")
            book_response = requests.get(f"{BASE_URL}/books/{book_id}/", headers=headers)
            print(f"图书详情响应状态: {book_response.status_code}")
            
            if book_response.status_code == 200:
                book_result = book_response.json()
                if book_result.get('code') == 1:
                    book_data = book_result['data']
                    print(f"图书名称: {book_data['book']['name']}")
                    print(f"现有评论数量: {len(book_data['comments'])}")
                    
                    # 3. 测试添加评论
                    print(f"\n3. 为图书 {book_id} 添加评论...")
                    comment_data = {
                        "content": "这是一本很好的书，内容丰富，值得推荐！"
                    }
                    
                    comment_response = requests.post(
                        f"{BASE_URL}/books/{book_id}/comments/", 
                        json=comment_data, 
                        headers=headers
                    )
                    print(f"添加评论响应状态: {comment_response.status_code}")
                    
                    if comment_response.status_code == 201:
                        comment_result = comment_response.json()
                        if comment_result.get('code') == 1:
                            print("✅ 评论添加成功！")
                            print(f"评论内容: {comment_result['data']['content']}")
                            print(f"评论用户: {comment_result['data']['username']}")
                            
                            # 4. 再次获取图书详情验证评论是否添加成功
                            print(f"\n4. 验证评论是否添加成功...")
                            verify_response = requests.get(f"{BASE_URL}/books/{book_id}/", headers=headers)
                            if verify_response.status_code == 200:
                                verify_result = verify_response.json()
                                if verify_result.get('code') == 1:
                                    new_comments = verify_result['data']['comments']
                                    print(f"更新后评论数量: {len(new_comments)}")
                                    if new_comments:
                                        latest_comment = new_comments[0]  # 最新评论应该在第一个
                                        print(f"最新评论: {latest_comment['content']}")
                                        print(f"评论用户: {latest_comment['username']}")
                                        print("✅ 评论功能测试成功！")
                                    else:
                                        print("❌ 未找到新添加的评论")
                                else:
                                    print(f"❌ 验证失败: {verify_result.get('msg')}")
                            else:
                                print(f"❌ 验证请求失败: {verify_response.status_code}")
                        else:
                            print(f"❌ 评论添加失败: {comment_result.get('msg')}")
                    else:
                        print(f"❌ 评论请求失败: {comment_response.status_code}")
                        try:
                            error_data = comment_response.json()
                            print(f"错误信息: {error_data}")
                        except:
                            print(f"响应内容: {comment_response.text}")
                else:
                    print(f"❌ 获取图书详情失败: {book_result.get('msg')}")
            else:
                print(f"❌ 图书详情请求失败: {book_response.status_code}")
        else:
            print(f"❌ 登录失败: {login_result.get('msg')}")
    else:
        print(f"❌ 登录请求失败: {login_response.status_code}")

if __name__ == "__main__":
    test_comment_api()
