import pandas as pd
from django.core.management.base import BaseCommand
from .models import *


def handle():
    df = pd.read_excel('1.xlsx')
    for i, row in df.iterrows():
        try:
            category_name = row['类型']
            category, created = Category.objects.get_or_create(name=category_name)
            
            # 创建Book实例
            book = Book(
                name=row['书名'],
                author=row['作者'],
                img_url=row['图片链接'],
                biy=row['标语'],
                description=row['简介'],
                price=row['价格'],
                nf=row['出版年份'],
                cbs=row['出版社'],
                des=row['简介'],  
                kcl=10,  
                content=None,  
                category=category
            )
            
            # 保存到数据库
            book.save()
            print(f'成功导入: {row["书名"]}')
        except Exception as e:
            print(f'导入失败 {row["书名"]}: {str(e)}')





def import_content_from_excel():
    # 读取Excel文件
    df = pd.read_excel('4.xlsx')
    
    # 遍历每一行数据
    for index, row in df.iterrows():
        book_name = row['图书名称']
        chapter_title = row['章节']
        chapter_content = row['内容']
        
        # 查找图书
        try:
            book = Book.objects.get(name=book_name)
            print(f"找到图书: {book_name}")
            
            # 创建章节内容
            Content.objects.create(
                book=book,
                title=chapter_title,
                content=chapter_content
            )
            print(f"已添加章节: {chapter_title}")
            
        except Book.DoesNotExist:
            print(f"未找到图书: {book_name}, 跳过此章节")
        except Exception as e:
            print(f"处理图书 {book_name} 时出错: {str(e)}")
    
    print("导入完成")



import random
from django.contrib.auth.models import User
from django.utils import timezone
from faker import Faker
import json
from .models import Profile, Order, Comment 
from decimal import Decimal
fake = Faker("zh-CN")
def generate_order_data(user):
    books = Book.objects.all()
    num_items = random.randint(1, 5)  
    selected_books = random.sample(list(books), num_items)
    
    order_items = []
    total = Decimal('0.00')
    
    for book in selected_books:
        quantity = random.randint(1, 3)
        item_total = Decimal(str(book.price)) * quantity
        
        order_items.append({
            'product_id': book.id,
            'product_name': book.name,
            'product_image': book.img_url,
            'product_price': str(book.price),
            'quantity': quantity,
            'item_total': str(item_total)
        })
        
        total += item_total
    
    return order_items, total

def main():
    user = User.objects.all()
    user.delete()
    provinces = ['贵州省', '新疆省', '广西省', '安徽省', '山东省', '山西省', '广东省', '江苏省', '江西省', '河北省', '河南省',
                 '浙江省', '海南省', '湖北省', '湖南省', '甘肃省', '福建省', '西藏自治区', '辽宁省', '重庆市', '陕西省',
                 '青海省', '黑龙江省', '吉林省', '云南省', '内蒙古自治区', '宁夏回族自治区', '四川省', '台湾省', '北京市', '天津市',
                 '上海市', '香港特别行政区', '澳门特别行政区', '南海诸岛']

    for i in range(100):
        username = "root"+ str(i) 
        email = "<EMAIL>"
        password = '123456'  
        user = User.objects.create_user(username=username, email=email, password=password)
        
        profile = Profile.objects.create(
            user=user,
            moneys=random.randint(5000, 10000),  
            isvip=random.choice([0, 1]),
            sex=random.choice([0, 1]),
            phone=fake.phone_number(),
            address=random.choice(provinces),
        )
        
        num_orders = random.randint(2, 5)
        for j in range(num_orders):
            order_items, total = generate_order_data(user)
            
            if profile.moneys < total:
                profile.moneys += total * 2 
                profile.save()
            profile.moneys -= total
            profile.save()
            
            order = Order(
                user=user,
                status=random.choice([0, 1]),  
                goods=json.dumps(order_items, ensure_ascii=False),
                total=str(total),
            )
            order.save()
            
        num_feedbacks = random.randint(1, 5)
        for k in range(num_feedbacks):
            feedback_type = random.choice(['bug', 'suggestion', 'question', 'other'])
            Comment.objects.create(
                user=user,
                email=fake.email() if random.choice([True, False]) else None,
                lx=feedback_type,
                cont=fake.paragraph(nb_sentences=3),
                reply=fake.paragraph(nb_sentences=2) if random.choice([True, False]) else None
            )

    print("Successfully created:")
    print(f"- {User.objects.count()} users")
    print(f"- {Book.objects.count()} books")
    print(f"- {Order.objects.count()} orders")
    print(f"- {Comment.objects.count()} feedback entries")



import pandas as pd
from .models import Book, Bookcomment  

def create_books():
    df = pd.read_excel('3.xlsx')  

    total = 0
    created = 0
    skipped = 0

    for index, row in df.iterrows():
        total += 1
        book_name = row['书名'].strip()  # 去除前后空格
        comment = row['comment']
        
        try:
            # 查找匹配的图书（精确匹配书名）
            book = Book.objects.get(name=book_name)
            
            # 创建评论
            Bookcomment.objects.create(
                book=book,
                content=comment
            )
            created += 1
            print(f'✅ 成功为《{book_name}》添加评论')
        except Book.DoesNotExist:
            skipped += 1
            print(f'⚠️ 未找到图书: {book_name}')
        except Book.MultipleObjectsReturned:
            skipped += 1
            print(f'❌ 找到多个同名图书: {book_name}')


    print(f'\n导入完成: 总计 {total} 条, 成功 {created} 条, 跳过 {skipped} 条')