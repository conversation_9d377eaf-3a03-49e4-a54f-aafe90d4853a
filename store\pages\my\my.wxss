/* pages/mine/mine.wxss */
.content {
	position: relative;
}

.userinfo {
	display: flex;
	flex-direction: row;
	align-items: center;
	color: rgb(22, 10, 10);
	position: absolute;
	z-index: 99999999;
	bottom: 20px;
	left: 20px;
}

.title {
	margin: 20rpx;
	margin-top: 50rpx;
	color: rgb(1, 19, 19);
	font-size: large;
}

.userinfo-avatar {
	overflow: hidden;
	width: 128rpx;
	height: 128rpx;
	margin: 20rpx;
	margin-top: 50rpx;
	border-radius: 50%;
}

.usermotto {
	margin-top: 200px;
}

.bg {
	width: 100%;
	background-color: rgb(202, 52, 177);
}



.wrapper {
  position: absolute;
  top: 0;
  bottom: 0;

  width: 100%;
  background-color: #f4f4f4;
}

.profile {
  height: 375rpx;
  background-color: #fff8f2a8;
  display: flex;
  justify-content: center;
	align-items: center;
}

    .avatar {
      display: block;
      width: 140rpx;
      height: 140rpx;
      border-radius: 50%;
      border: 2rpx solid rgb(236, 112, 112);
      overflow: hidden;
    }

    .nickname {
      display: block;
      text-align: center;
      margin-top: 20rpx;
      font-size: 30rpx;
      color: rgb(0, 0, 0);
    }

/* General styles */
body {
  font-family: Arial, sans-serif;
  background-color: #f5f5f5;
  margin: 0;
  padding: 0;
}

/* Count section */
.count {
  display: flex;
  justify-content: space-between;
  background-color: #ffffff;
  padding: 20px;
  margin: 10px 0;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.cell {
  text-align: center;
  flex: 1;
  font-size: 18px;
  color: #333;
}

.cell text {
  display: block;
  margin-top: 5px;
  font-size: 14px;
  color: #999;
}

/* Orders section */
.orders {
  background-color: #ffffff;
  padding: 15px;
  margin: 10px 0;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.orders .title {
  font-size: 20px;
  color: #333;
  font-weight: bold;
}

/* Address management */
.address {
  background-color: #ffffff;
  padding: 15px;
  margin: 10px 0;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  font-size: 16px;
  color: #333;
  position: relative;
}

.address::after {
  content: '';
  position: absolute;
  right: 15px;
  top: 50%;
  width: 10px;
  height: 10px;
  border-right: 2px solid #999;
  border-bottom: 2px solid #999;
  transform: rotate(-45deg) translateY(-50%);
}

