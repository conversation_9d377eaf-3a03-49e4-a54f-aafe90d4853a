#!/usr/bin/env python3
"""
检查Profile表字段是否正确更新
"""
import os
import sys
import django

# 设置Django环境
sys.path.append('un')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'un.settings')
django.setup()

from customer.models import Profile
from django.contrib.auth import get_user_model

def check_profile_fields():
    """检查Profile表字段"""
    
    print("=== Profile表字段检查 ===")
    
    # 检查模型字段
    print("1. 模型字段:")
    for field in Profile._meta.fields:
        print(f"   - {field.name}: {field.__class__.__name__}")
    
    # 检查是否有数据
    print(f"\n2. 数据统计:")
    total_profiles = Profile.objects.count()
    print(f"   总Profile记录数: {total_profiles}")
    
    if total_profiles > 0:
        print("\n3. 示例数据:")
        profiles = Profile.objects.all()[:5]
        for profile in profiles:
            print(f"   用户: {profile.user.username}")
            print(f"   省份: {getattr(profile, 'province', '字段不存在')}")
            print(f"   手机: {profile.phone}")
            print("   ---")

if __name__ == "__main__":
    check_profile_fields()
