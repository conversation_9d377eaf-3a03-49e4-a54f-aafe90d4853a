// pages/bookRead/bookRead.js
const app = getApp();
Page({
  data: {
    book: {},
    contents: [],
    current_content: {},
    showSidebar: false,
    isNightMode: false,
    currentIndex: 0
  },

  onLoad(options) {
    const { id } = options;
    this.loadBookData(id);
  },

  loadBookData(bookId, contentId = null) {
    const baseUrl = 'http://127.0.0.1:8000';
    let url = `/api/books/${bookId}/read/`;
    if (contentId) url += `?content_id=${contentId}`;
    
        // 完整URL
    const fullUrl = baseUrl +  url;
    wx.request({
      url: fullUrl,
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log('图书阅读API响应:', res);

        if (res.data.code === 1) {
          const { book, contents, current_content } = res.data.data;

          // 验证数据完整性
          if (!contents || !Array.isArray(contents)) {
            console.error('contents数据无效:', contents);
            wx.showToast({
              title: '章节数据加载失败',
              icon: 'none'
            });
            return;
          }

          if (!current_content) {
            console.error('current_content数据无效:', current_content);
            wx.showToast({
              title: '当前章节数据加载失败',
              icon: 'none'
            });
            return;
          }

          const currentIndex = contents.findIndex(c => c.id === current_content.id);

          this.setData({
            book,
            contents,
            current_content,
            currentIndex: currentIndex >= 0 ? currentIndex : 0
          });
        } else {
          console.error('API返回错误:', res.data);
          wx.showToast({
            title: res.data.msg || '加载失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 切换章节
  selectChapter(e) {
    const contentId = e.currentTarget.dataset.id;
    const { book } = this.data;

    if (!book || !book.id) {
      wx.showToast({
        title: '图书数据加载中',
        icon: 'none'
      });
      return;
    }

    if (!contentId) {
      wx.showToast({
        title: '章节ID无效',
        icon: 'none'
      });
      return;
    }

    this.loadBookData(book.id, contentId);
    this.toggleSidebar();
  },

  // 上一章
  prevChapter() {
    const { contents, currentIndex, book } = this.data;

    // 验证数据完整性
    if (!contents || !Array.isArray(contents) || !book || !book.id) {
      wx.showToast({
        title: '数据加载中，请稍后',
        icon: 'none'
      });
      return;
    }

    if (currentIndex > 0) {
      const prevContent = contents[currentIndex - 1];
      if (prevContent && prevContent.id) {
        this.loadBookData(book.id, prevContent.id);
      } else {
        wx.showToast({
          title: '章节数据错误',
          icon: 'none'
        });
      }
    } else {
      wx.showToast({
        title: '已经是第一章了',
        icon: 'none'
      });
    }
  },

  // 下一章
  nextChapter() {
    const { contents, currentIndex, book } = this.data;

    // 验证数据完整性
    if (!contents || !Array.isArray(contents) || !book || !book.id) {
      wx.showToast({
        title: '数据加载中，请稍后',
        icon: 'none'
      });
      return;
    }

    if (currentIndex < contents.length - 1) {
      const nextContent = contents[currentIndex + 1];
      if (nextContent && nextContent.id) {
        this.loadBookData(book.id, nextContent.id);
      } else {
        wx.showToast({
          title: '章节数据错误',
          icon: 'none'
        });
      }
    } else {
      wx.showToast({
        title: '已经是最后一章了',
        icon: 'none'
      });
    }
  },

  // 切换目录侧边栏
  toggleSidebar() {
    this.setData({
      showSidebar: !this.data.showSidebar
    });
  },

  // 切换日夜模式
  toggleNightMode() {
    this.setData({
      isNightMode: !this.data.isNightMode
    });
  }
});