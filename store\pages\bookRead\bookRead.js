// pages/bookRead/bookRead.js
const app = getApp();
Page({
  data: {
    book: {},
    contents: [],
    current_content: {},
    showSidebar: false,
    isNightMode: false,
    currentIndex: 0
  },

  onLoad(options) {
    const { id } = options;
    this.loadBookData(id);
  },

  loadBookData(bookId, contentId = null) {
    const baseUrl = 'http://127.0.0.1:8000';
    let url = `/api/books/${bookId}/read/`;
    if (contentId) url += `?content_id=${contentId}`;
    
        // 完整URL
    const fullUrl = baseUrl +  url;
    wx.request({
      url: fullUrl,
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success: (res) => {
        const { book, contents, current_content } = res.data;
        const currentIndex = contents.findIndex(c => c.id === current_content.id);
        
        this.setData({
          book,
          contents,
          current_content,
          currentIndex
        });
      }
    });
  },

  // 切换章节
  selectChapter(e) {
    const contentId = e.currentTarget.dataset.id;
    this.loadBookData(this.data.book.id, contentId);
    this.toggleSidebar();
  },

  // 上一章
  prevChapter() {
    if (this.data.currentIndex > 0) {
      const prevContent = this.data.contents[this.data.currentIndex - 1];
      this.loadBookData(this.data.book.id, prevContent.id);
    } else {
      wx.showToast({
        title: '已经是第一章了',
        icon: 'none'
      });
    }
  },

  // 下一章
  nextChapter() {
    if (this.data.currentIndex < this.data.contents.length - 1) {
      const nextContent = this.data.contents[this.data.currentIndex + 1];
      this.loadBookData(this.data.book.id, nextContent.id);
    } else {
      wx.showToast({
        title: '已经是最后一章了',
        icon: 'none'
      });
    }
  },

  // 切换目录侧边栏
  toggleSidebar() {
    this.setData({
      showSidebar: !this.data.showSidebar
    });
  },

  // 切换日夜模式
  toggleNightMode() {
    this.setData({
      isNightMode: !this.data.isNightMode
    });
  }
});