<swiper class="swiper" indicator-dots="true" autoplay="true" interval="3000" duration="1000" circular="true">
  <block wx:for="{{movies}}" wx:key="1" wx-index="index">
    <swiper-item>
      <image src="{{item.url}}" class="slide-image" mode="aspectFill" />
    </swiper-item>
  </block>
</swiper>

<van-notice-bar color="#1989fa" background="#ecf9ff" left-icon="volume-o" text="三更灯火五更鸡，正是男儿读书时。黑发不知勤学早，白首方悔读书迟。" />



<!-- 热门图书 -->
<view class="section">
  <view class="section-header">
    <text class="section-title">热门图书</text>
    <view class="more-link" bindtap="navigateToMore" data-type="hot">
      更多 >>>
    </view>
  </view>
  <view class="book-list">
    <block wx:for="{{hotBooks}}" wx:key="id">
      <view class="book-item" bindtap="navigateToDetail" data-id="{{item.id}}">
        <image class="book-cover" src="{{item.img_url}}" mode="aspectFill"></image>
        <view class="book-info">
          <text class="book-title">{{item.name}}</text>
          <text class="book-author">{{item.author}}</text>
          <text class="book-price">¥{{item.price}}</text>
        </view>
      </view>
    </block>
  </view>
</view>

<!-- 推荐图书 -->
<view class="section">
  <view class="section-header">
    <text class="section-title">推荐图书</text>
    <view class="more-link" bindtap="navigateToMore" data-type="recommend">
      更多 >>>
    </view>
  </view>
  <view class="book-list">
    <block wx:for="{{recommendBooks}}" wx:key="id">
      <view class="book-item" bindtap="navigateToDetail" data-id="{{item.id}}">
        <image class="book-cover" src="{{item.img_url}}" mode="aspectFill"></image>
        <view class="book-info">
          <text class="book-title">{{item.name}}</text>
          <text class="book-author">{{item.author}}</text>
          <text class="book-price">¥{{item.price}}</text>
        </view>
      </view>
    </block>
  </view>
</view>


