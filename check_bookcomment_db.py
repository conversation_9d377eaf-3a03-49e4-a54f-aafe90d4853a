#!/usr/bin/env python3
"""
检查数据库中的bookcomment记录
"""
import os
import sys
import django

# 设置Django环境
sys.path.append('un')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'un.settings')
django.setup()

from customer.models import Bookcomment, Book
from django.contrib.auth import get_user_model

def check_bookcomment_records():
    """检查bookcomment表记录"""
    
    User = get_user_model()
    
    print("=== Bookcomment表记录检查 ===")
    
    # 1. 检查表结构
    print("1. 表字段:")
    for field in Bookcomment._meta.fields:
        print(f"   - {field.name}: {field.__class__.__name__}")
    
    # 2. 统计记录数
    total_comments = Bookcomment.objects.count()
    print(f"\n2. 总评论数: {total_comments}")
    
    # 3. 显示最新的10条记录
    print("\n3. 最新的10条评论记录:")
    latest_comments = Bookcomment.objects.all().order_by('-id')[:10]
    
    for comment in latest_comments:
        try:
            print(f"   ID: {comment.id}")
            print(f"   图书: {comment.book.name if comment.book else 'None'}")
            print(f"   用户: {comment.user.username if hasattr(comment, 'user') and comment.user else 'None'}")
            print(f"   内容: {comment.content[:50]}...")
            print(f"   时间: {getattr(comment, 'create_time', 'None')}")
            print("   ---")
        except Exception as e:
            print(f"   记录ID {comment.id} 读取错误: {e}")
    
    # 4. 按图书分组统计
    print("\n4. 按图书分组的评论统计:")
    books_with_comments = Book.objects.filter(comments__isnull=False).distinct()
    for book in books_with_comments[:5]:  # 只显示前5本有评论的书
        comment_count = Bookcomment.objects.filter(book=book).count()
        print(f"   《{book.name}》: {comment_count} 条评论")

if __name__ == "__main__":
    check_bookcomment_records()
