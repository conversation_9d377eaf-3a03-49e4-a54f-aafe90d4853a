
<view class="container {{isNightMode ? 'night-mode' : ''}}">
  <!-- 顶部图书信息 -->
  <view class="header">
    <view class="mode-switch" bindtap="toggleNightMode">
      <text>{{isNightMode ? '☀️' : '🌙'}}</text>
    </view>
  </view>
  
  <!-- 章节选择侧边栏 -->
  <view class="sidebar {{showSidebar ? 'show' : ''}}">
    <view class="sidebar-header">
      <text>目录</text>
      <view class="close-btn" bindtap="toggleSidebar">×</view>
    </view>
    <scroll-view class="chapter-list" scroll-y>
      <view 
        wx:for="{{contents}}" 
        wx:key="id"
        class="chapter-item {{current_content.id === item.id ? 'active' : ''}}"
        bindtap="selectChapter"
        data-id="{{item.id}}"
      >
        {{item.title}}
      </view>
    </scroll-view>
  </view>
  
  <!-- 阅读内容区域 -->
  <view class="content-area">
    <view class="chapter-title">{{current_content.title}}</view>
    <scroll-view class="content-text" scroll-y>
      <rich-text nodes="{{current_content.content}}"></rich-text>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="footer">
      <view class="btn prev-btn" bindtap="prevChapter">上一章</view>
      <view class="btn menu-btn" bindtap="toggleSidebar">目录</view>
      <view class="btn next-btn" bindtap="nextChapter">下一章</view>
    </view>
  </view>
  
  <!-- 侧边栏遮罩 -->
  <view 
    class="sidebar-mask {{showSidebar ? 'show' : ''}}" 
    bindtap="toggleSidebar"
  ></view>
</view>