const app = getApp();
Page({
  data: {
    bookId: null,
    book: {}, 
    activeTab: 'description',
    isFavorite: false,
    loading: true
  },

  onLoad(options) {
    this.setData({ bookId: options.id })
    this.loadBookDetail()
  },

  // 加载图书详情
  loadBookDetail() {
    this.setData({ loading: true })
    
    wx.request({
      url: `http://127.0.0.1:8000/books/${this.data.bookId}/`,
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success: (res) => {
        if (res.data.code === 1) {
          console.log(res.data.data);
          this.setData({ 
            book: res.data.data,
            loading: false
          })
        } else {
          wx.showToast({
            title: res.data.msg || '加载失败',
            icon: 'none'
          })
          setTimeout(() => wx.navigateBack(), 1500)
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
        setTimeout(() => wx.navigateBack(), 1500)
      }
    })
  },


  // 切换选项卡
  switchTab(e) {
    this.setData({ activeTab: e.currentTarget.dataset.tab })
  },



  addToCart: function() {
    const that = this;
    wx.request({
      url: 'http://127.0.0.1:8000/cart/',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      data: {
        book_id: that.data.book.book.id,
        count: 1
      },
      success(res) {
        if (res.data.code === 1) {
          wx.showToast({
            title: '已加入借阅车',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: res.data.msg,
            icon: 'none'
          });
        }
      },
      fail(err) {
        wx.showToast({
          title: '加入借阅车失败',
          icon: 'none'
        });
      }
    });
  },

  toggleFavorite: function() {
    const that = this;
    wx.request({
      url: `http://127.0.0.1:8000/favorites/${that.data.book.book.id}/`,
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success(res) {
        if (res.data.code === 1) {
          that.setData({
            is_favorited: res.data.data.is_favorited
          });
          wx.showToast({
            title: res.data.msg,
            icon: 'none'
          });
        } else {
          wx.showToast({
            title: res.data.msg,
            icon: 'none'
          });
        }
      },
      fail(err) {
        wx.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    });
  },
  
  borrowBook: function() {
    // 立即借阅逻辑
    wx.showToast({
      title: '立即借阅功能开发中',
      icon: 'none'
    });
  }
})