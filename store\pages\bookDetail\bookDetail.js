const app = getApp();
Page({
  data: {
    bookId: null,
    book: {},
    activeTab: 'description',
    isFavorite: false,
    loading: true,
    commentText: '',
    canSubmitComment: false
  },

  onLoad(options) {
    this.setData({ bookId: options.id })
    this.loadBookDetail()
  },

  // 加载图书详情
  loadBookDetail() {
    this.setData({ loading: true })
    
    wx.request({
      url: `http://127.0.0.1:8000/books/${this.data.bookId}/`,
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success: (res) => {
        if (res.data.code === 1) {
          console.log(res.data.data);
          this.setData({ 
            book: res.data.data,
            loading: false
          })
        } else {
          wx.showToast({
            title: res.data.msg || '加载失败',
            icon: 'none'
          })
          setTimeout(() => wx.navigateBack(), 1500)
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
        setTimeout(() => wx.navigateBack(), 1500)
      }
    })
  },


  // 切换选项卡
  switchTab(e) {
    this.setData({ activeTab: e.currentTarget.dataset.tab })
  },



  addToCart: function() {
    const that = this;
    wx.request({
      url: 'http://127.0.0.1:8000/cart/',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      data: {
        book_id: that.data.book.book.id,
        count: 1
      },
      success(res) {
        if (res.data.code === 1) {
          wx.showToast({
            title: '已加入借阅车',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: res.data.msg,
            icon: 'none'
          });
        }
      },
      fail(err) {
        wx.showToast({
          title: '加入借阅车失败',
          icon: 'none'
        });
      }
    });
  },

  toggleFavorite: function() {
    const that = this;
    wx.request({
      url: `http://127.0.0.1:8000/favorites/${that.data.book.book.id}/`,
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success(res) {
        if (res.data.code === 1) {
          that.setData({
            is_favorited: res.data.data.is_favorited
          });
          wx.showToast({
            title: res.data.msg,
            icon: 'none'
          });
        } else {
          wx.showToast({
            title: res.data.msg,
            icon: 'none'
          });
        }
      },
      fail(err) {
        wx.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    });
  },
  
  borrowBook: function() {
    // 立即借阅逻辑
    wx.showToast({
      title: '立即借阅功能开发中',
      icon: 'none'
    });
  },

  // 评论输入
  onCommentInput: function(e) {
    const value = e.detail.value;
    this.setData({
      commentText: value,
      canSubmitComment: value.trim().length > 0
    });
  },

  // 提交评论
  submitComment: function() {
    const { commentText, bookId } = this.data;

    console.log('=== 提交评论调试信息 ===');
    console.log('commentText:', commentText);
    console.log('bookId:', bookId);
    console.log('accessToken:', app.globalData.accessToken);

    if (!commentText.trim()) {
      wx.showToast({
        title: '请输入评论内容',
        icon: 'none'
      });
      return;
    }

    // 检查是否已登录
    if (!app.globalData.accessToken) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      // 跳转到登录页面
      wx.navigateTo({
        url: '/pages/login/login'
      });
      return;
    }

    wx.showLoading({ title: '提交中...' });

    const requestUrl = `http://127.0.0.1:8000/books/${bookId}/comments/`;
    const requestData = {
      content: commentText.trim()
    };

    console.log('发送请求:', requestUrl);
    console.log('请求数据:', requestData);

    wx.request({
      url: requestUrl,
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      data: requestData,
      success: (res) => {
        console.log('请求成功响应:', res);
        wx.hideLoading();
        if (res.data.code === 1) {
          wx.showToast({
            title: '评论发表成功',
            icon: 'success'
          });
          // 清空输入框并重置按钮状态
          this.setData({
            commentText: '',
            canSubmitComment: false
          });
          // 重新加载图书详情以获取最新评论
          this.loadBookDetail();
        } else {
          wx.showToast({
            title: res.data.msg || '评论发表失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.log('请求失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '网络错误: ' + (err.errMsg || '未知错误'),
          icon: 'none'
        });
      }
    });
  },

  // 格式化时间显示
  formatTime: function(timeStr) {
    if (!timeStr) return '';
    const date = new Date(timeStr);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前';
    } else {
      return date.toLocaleDateString();
    }
  }
})