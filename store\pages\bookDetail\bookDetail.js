var app = getApp();
Page({
  data: {
    bookId: null,
    book: {},
    activeTab: 'description',
    isFavorite: false,
    loading: true,
    commentText: '',
    canSubmitComment: false
  },

  onLoad: function(options) {
    this.setData({ bookId: options.id })

    // 检查用户是否已登录
    if (!app.globalData.accessToken) {
      wx.showModal({
        title: '提示',
        content: '请先登录后查看图书详情',
        showCancel: false,
        success: function() {
          wx.navigateTo({
            url: '/pages/login/login'
          })
        }
      })
      return;
    }

    this.loadBookDetail()
  },

  // 加载图书详情
  loadBookDetail: function() {
    var self = this;
    this.setData({ loading: true })

    // 检查是否有token
    if (!app.globalData.accessToken) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(function() {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);
      return;
    }

    wx.request({
      url: 'http://127.0.0.1:8000/books/' + this.data.bookId + '/',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success: function(res) {
        if (res.data.code === 1) {
          console.log(res.data.data);
          self.setData({
            book: res.data.data,
            loading: false
          })
        } else {
          wx.showToast({
            title: res.data.msg || '加载失败',
            icon: 'none'
          })
          setTimeout(function() { wx.navigateBack(); }, 1500)
        }
      },
      fail: function(err) {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
        setTimeout(function() { wx.navigateBack(); }, 1500)
      }
    })
  },


  // 切换选项卡
  switchTab: function(e) {
    this.setData({ activeTab: e.currentTarget.dataset.tab })
  },



  addToCart: function() {
    var that = this;
    wx.request({
      url: 'http://127.0.0.1:8000/cart/',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      data: {
        book_id: that.data.book.book.id,
        count: 1
      },
      success: function(res) {
        if (res.data.code === 1) {
          wx.showToast({
            title: '已加入借阅车',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: res.data.msg,
            icon: 'none'
          });
        }
      },
      fail: function(err) {
        wx.showToast({
          title: '加入借阅车失败',
          icon: 'none'
        });
      }
    });
  },

  toggleFavorite: function() {
    var that = this;
    wx.request({
      url: 'http://127.0.0.1:8000/favorites/' + that.data.book.book.id + '/',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success: function(res) {
        if (res.data.code === 1) {
          that.setData({
            is_favorited: res.data.data.is_favorited
          });
          wx.showToast({
            title: res.data.msg,
            icon: 'none'
          });
        } else {
          wx.showToast({
            title: res.data.msg,
            icon: 'none'
          });
        }
      },
      fail: function(err) {
        wx.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    });
  },
  
  borrowBook: function() {
    // 立即借阅逻辑
    wx.showToast({
      title: '立即借阅功能开发中',
      icon: 'none'
    });
  },

  // 评论输入
  onCommentInput: function(e) {
    var value = e.detail.value;
    this.setData({
      commentText: value,
      canSubmitComment: value.trim().length > 0
    });
  },

  // 提交评论
  submitComment: function() {
    var self = this;
    var commentText = this.data.commentText;
    var bookId = this.data.bookId;

    if (!commentText.trim()) {
      wx.showToast({
        title: '请输入评论内容',
        icon: 'none'
      });
      return;
    }

    // 检查是否已登录
    if (!app.globalData.accessToken) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(function() {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);
      return;
    }

    wx.showLoading({ title: '提交中...' });

    var requestUrl = 'http://127.0.0.1:8000/books/' + bookId + '/comments/';
    var requestData = {
      content: commentText.trim()
    };

    console.log('发送请求:', requestUrl);
    console.log('请求数据:', requestData);

    wx.request({
      url: requestUrl,
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      data: requestData,
      success: function(res) {
        console.log('请求成功响应:', res);
        wx.hideLoading();
        if (res.data.code === 1) {
          wx.showToast({
            title: '评论发表成功',
            icon: 'success'
          });
          // 清空输入框并重置按钮状态
          self.setData({
            commentText: '',
            canSubmitComment: false
          });
          // 重新加载图书详情以获取最新评论
          self.loadBookDetail();
        } else {
          wx.showToast({
            title: res.data.msg || '评论发表失败',
            icon: 'none'
          });
        }
      },
      fail: function(err) {
        console.log('请求失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '网络错误: ' + (err.errMsg || '未知错误'),
          icon: 'none'
        });
      }
    });
  },

  // 格式化时间显示
  formatTime: function(timeStr) {
    if (!timeStr) return '';
    var date = new Date(timeStr);
    var now = new Date();
    var diff = now - date;

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前';
    } else {
      return date.toLocaleDateString();
    }
  }
})