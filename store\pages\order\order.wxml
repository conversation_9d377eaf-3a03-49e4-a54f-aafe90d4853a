<view class="container">
  <block wx:for="{{orders}}" wx:key="order_num">
    <view class="order-item" bindtap="onOrderClick" data-ordernum="{{item.order_num}}">
      <text class="order-number">订单号: {{item.order_num}}</text>
      <text class="order-total">总价: ¥{{item.total}}</text>
      <text class="order-status" data-status="{{item.status}}">状态: {{item.status === 0 ? '未支付' : '已支付'}}</text>
      <text class="order-time">创建时间: {{item.create_time}}</text>
    </view>
  </block>
</view>
