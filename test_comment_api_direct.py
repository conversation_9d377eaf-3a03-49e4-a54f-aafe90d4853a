#!/usr/bin/env python3
"""
直接测试评论API
"""
import os
import sys
import django

# 设置Django环境
sys.path.append('un')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'un.settings')
django.setup()

from customer.models import Book, User, Bookcomment
from customer.serializers import BookCommentSerializer
from django.contrib.auth import get_user_model

def test_comment_creation():
    print("=== 直接测试评论创建 ===")
    
    # 获取第一本书
    book = Book.objects.first()
    if not book:
        print("❌ 没有找到图书数据")
        return
    
    print(f"测试图书: {book.name} (ID: {book.id})")
    
    # 获取第一个用户
    User = get_user_model()
    user = User.objects.first()
    if not user:
        print("❌ 没有找到用户数据")
        return
    
    print(f"测试用户: {user.username}")
    
    # 创建评论数据
    comment_data = {
        'content': '这是一个测试评论，用于验证API功能是否正常工作。'
    }
    
    # 模拟request对象
    class MockRequest:
        def __init__(self, user):
            self.user = user
    
    mock_request = MockRequest(user)
    
    # 测试序列化器
    print("\n1. 测试序列化器...")
    serializer = BookCommentSerializer(data=comment_data, context={'request': mock_request})
    
    if serializer.is_valid():
        print("✅ 序列化器验证通过")
        
        # 保存评论
        print("2. 保存评论...")
        comment = serializer.save(book=book)
        print(f"✅ 评论保存成功，ID: {comment.id}")
        print(f"   内容: {comment.content}")
        print(f"   用户: {comment.user.username}")
        print(f"   图书: {comment.book.name}")
        print(f"   创建时间: {comment.create_time}")
        
        # 验证数据库中的数据
        print("\n3. 验证数据库...")
        saved_comment = Bookcomment.objects.get(id=comment.id)
        print(f"✅ 数据库中找到评论: {saved_comment.content[:30]}...")
        
        # 检查该书的评论总数
        total_comments = Bookcomment.objects.filter(book=book).count()
        print(f"✅ 该书总评论数: {total_comments}")
        
    else:
        print("❌ 序列化器验证失败:")
        print(serializer.errors)

def test_comment_retrieval():
    print("\n=== 测试评论检索 ===")
    
    book = Book.objects.first()
    comments = Bookcomment.objects.filter(book=book).order_by('-create_time')
    
    print(f"图书 '{book.name}' 的评论:")
    for i, comment in enumerate(comments[:5], 1):  # 显示前5条
        user_name = comment.user.username if comment.user else "匿名"
        create_time = comment.create_time.strftime('%Y-%m-%d %H:%M:%S') if comment.create_time else "未知时间"
        print(f"  {i}. {user_name} ({create_time}): {comment.content[:50]}...")

if __name__ == "__main__":
    test_comment_creation()
    test_comment_retrieval()
