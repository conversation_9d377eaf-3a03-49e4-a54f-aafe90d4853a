
.container {
  height: 100vh;
  background: #f8f5ee;
  color: #333;
  transition: all 0.3s;
  margin-top: -150rpx;
}


.container.night-mode {
  background: #2a2a2a;
  color: #ccc;
}

/* 头部样式 */
.header {
  display: flex;
  padding: 20rpx;
  border-bottom: 1rpx solid #eaeaea;
  position: relative;

}

.night-mode .header {
  border-color: #444;
}

.book-cover {
  width: 100rpx;
  height: 140rpx;
  margin-right: 20rpx;
}

.book-info {
  flex: 1;
}

.book-title {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
}

.book-author {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.night-mode .book-author {
  color: #888;
}

.mode-switch {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40rpx;
}

/* 侧边栏样式 */
.sidebar {
  position: fixed;
  left: -70%;
  top: 0;
  bottom: 0;
  width: 70%;
  background: #fff;
  z-index: 100;
  transition: transform 0.3s;
  box-shadow: 2rpx 0 10rpx rgba(0,0,0,0.1);
}

.night-mode .sidebar {
  background: #333;
  color: #eee;
}

.sidebar.show {
  transform: translateX(100%);
}

.sidebar-header {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
}

.night-mode .sidebar-header {
  border-color: #444;
}

.close-btn {
  font-size: 40rpx;
  line-height: 1;
}

.chapter-list {
  height: calc(100vh - 120rpx);
}

.chapter-item {
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.night-mode .chapter-item {
  border-color: #444;
}

.chapter-item.active {
  color: #07c160;
  background: rgba(7, 193, 96, 0.1);
}

/* 内容区域样式 */
.content-area {
  padding: 30rpx;
  height: calc(100vh - 220rpx);
}

.chapter-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-align: center;
}

.content-text {
  font-size: 32rpx;
  line-height: 1.8;
  height: calc(100vh - 350rpx);
}

.night-mode .content-text {
  color: #ddd;
}

/* 底部操作栏 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #eee;
}

.night-mode .footer {
  background: #333;
  border-color: #444;
}

.btn {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  padding: 20rpx;
}

.menu-btn {
  border-left: 1rpx solid #eee;
  border-right: 1rpx solid #eee;
}

.night-mode .menu-btn {
  border-color: #444;
}

/* 遮罩层 */
.sidebar-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  z-index: 99;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
}

.sidebar-mask.show {
  opacity: 1;
  pointer-events: auto;
}