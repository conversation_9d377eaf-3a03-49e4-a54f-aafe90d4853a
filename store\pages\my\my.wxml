<!--pages/mine/mine.wxml-->

<view class="wrapper">
  <!-- 个人资料 -->
  <view class="profile" bindtap="chooseImage">
    <view class="meta">
      <image class="avatar" src="{{userInfo.avatar_url || '/images/default-avatar.png'}}"></image>
      <text class="nickname">{{userInfo.username}}</text>
    </view>
  </view>
  <!-- 统计 -->
  <view class="count">
    <view class="cell"> {{userInfo.moneys }} <text>余额</text> </view>
    <view class="cell" bindtap="profile">
  {{ userInfo.isvip === 1 ? '会员' : '非会员' }}

  <text>类型</text>
</view>

    
  </view>

  <view class="address icon-arrow" bindtap="recharge"><van-icon name="balance-pay" /> 余额充值</view>
<view class="address icon-arrow" bindtap="order"><van-icon name="orders-o" /> 订单中心</view>
<view class="address icon-arrow" bindtap="mybook"><van-icon name="bookmark-o" /> 我的图书</view>
<view class="address icon-arrow" bindtap="feedback"><van-icon name="comment-o" /> 反馈中心</view>
</view>


