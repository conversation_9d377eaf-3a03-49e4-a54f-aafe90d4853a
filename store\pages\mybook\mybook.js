const app = getApp();
Page({
  data: {
    books: []
  },
  
  onLoad() {
    this.loadMyBooks();
  },
  
  loadMyBooks() {
    wx.showLoading({ title: '加载中...' });
    wx.request({
      url: 'http://127.0.0.1:8000/api/mybooks/',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log(res.data.data);
        if (res.data.code === 1) {
          this.setData({ books: res.data.data });
        } else {
          wx.showToast({ title: res.data.msg, icon: 'none' });
        }
      },
      fail: (err) => {
        wx.showToast({ title: '网络错误', icon: 'none' });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },
  
  navigateToDetail(e) {
    const bookId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/bookRead/bookRead?id=${bookId}`
    });
  }
});