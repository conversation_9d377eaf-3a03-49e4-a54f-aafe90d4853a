{% extends 'base.html' %}
{% block content %}
{% load static %}

<style>



.container {
    max-width: 90%;
    margin: 0 auto;
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    margin-top: 80px;
}

h2 {
    color: #2c3e50;
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #eee;
    font-size: 28px;
}

/* 表格样式 */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 25px 0;
    font-size: 16px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

thead tr {
    background-color: #3498db;
    color: white;
    text-align: left;
}

th, td {
    padding: 15px 20px;
    text-align: left;
}

th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 14px;
}

tbody tr {
    border-bottom: 1px solid #eee;
    transition: all 0.2s;
}

tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

tbody tr:last-child {
    border-bottom: 2px solid #3498db;
}

tbody tr:hover {
    background-color: #f1f9ff;
}

/* 总价样式 */
p {
    font-size: 18px;
    text-align: right;
    margin: 20px 0;
}

p strong {
    color: #2c3e50;
}

/* 按钮样式 */
.button {
    display: inline-block;
    background: #3498db;
    color: white;
    padding: 12px 25px;
    text-decoration: none;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 16px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.button:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    th, td {
        padding: 10px 12px;
        font-size: 14px;
    }
    
    .button {
        padding: 10px 20px;
    }
}
</style>

<div class="container">
    <h2>订单详情</h2>
    <table>
        <thead>
            <tr>
                <th>图书名称</th>
                <th>价格</th>
                <th>数量</th>
                <th>小计</th>
            </tr>
        </thead>
        <tbody>
            {% for item in order_goods %}
            <tr>
                <td>{{ item.product_name }}</td>
                <td>{{ item.product_price }}</td>
                <td>{{ item.quantity }}</td>
                <td>{{ item.item_total }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- 总价 -->
    <p><strong>订单总价: </strong>{{ order.total }}</p>
    <br>

    <a href="{% url 'aorder_list' %}" class="button">返回订单列表</a>
</div>


{% endblock %}