
<!-- NOTE: DO NOT EDIT THE FOLLOWING SECTION DIRECTLY! It is autogenerated via the `build-customizer-html` Grunt task using the customizer-nav.pug template.-->
<li><a href="#import-drop-target">Import</a></li>
<li><a href="#less">Less components</a></li>
<li><a href="#plugins">jQuery plugins</a></li>
<li><a href="#less-variables">Less variables</a>
  <ul class="nav">
    <li><a href="#colors">Colors</a></li>
    <li><a href="#scaffolding">Scaffolding</a></li>
    <li><a href="#typography">Typography</a></li>
    <li><a href="#iconography">Iconography</a></li>
    <li><a href="#components">Components</a></li>
    <li><a href="#tables">Tables</a></li>
    <li><a href="#buttons">Buttons</a></li>
    <li><a href="#forms">Forms</a></li>
    <li><a href="#dropdowns">Dropdowns</a></li>
    <li><a href="#media-queries-breakpoints">Media queries breakpoints</a></li>
    <li><a href="#grid-system">Grid system</a></li>
    <li><a href="#container-sizes">Container sizes</a></li>
    <li><a href="#navbar">Navbar</a></li>
    <li><a href="#navs">Navs</a></li>
    <li><a href="#tabs">Tabs</a></li>
    <li><a href="#pills">Pills</a></li>
    <li><a href="#pagination">Pagination</a></li>
    <li><a href="#pager">Pager</a></li>
    <li><a href="#jumbotron">Jumbotron</a></li>
    <li><a href="#form-states-and-alerts">Form states and alerts</a></li>
    <li><a href="#tooltips">Tooltips</a></li>
    <li><a href="#popovers">Popovers</a></li>
    <li><a href="#labels">Labels</a></li>
    <li><a href="#modals">Modals</a></li>
    <li><a href="#alerts">Alerts</a></li>
    <li><a href="#progress-bars">Progress bars</a></li>
    <li><a href="#list-group">List group</a></li>
    <li><a href="#panels">Panels</a></li>
    <li><a href="#thumbnails">Thumbnails</a></li>
    <li><a href="#wells">Wells</a></li>
    <li><a href="#badges">Badges</a></li>
    <li><a href="#breadcrumbs">Breadcrumbs</a></li>
    <li><a href="#carousel">Carousel</a></li>
    <li><a href="#close">Close</a></li>
    <li><a href="#code">Code</a></li>
    <li><a href="#type">Type</a></li>
  </ul>
</li>
<li><a href="#download">Download</a></li>
<!-- NOTE: DO NOT EDIT THE PRECEDING SECTION DIRECTLY! It is autogenerated via the `build-customizer-html` Grunt task using the customizer-nav.pug template.-->