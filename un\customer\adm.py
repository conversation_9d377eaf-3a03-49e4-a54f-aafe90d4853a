from django.contrib.auth import authenticate, login
from django.contrib.auth.models import User
from django.shortcuts import render, redirect
from .models import Book, Category
from django.db.models import Q
from .models import *
from django.contrib.auth import logout


def alogin(request):
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            return redirect('ahome')
        else:
            error = "用户名或密码错误"
            return render(request, "login.html", {"error": error})
    
    return render(request, "login.html")



def alogout(request):
    logout(request) 
    return redirect('alogin')



from django.shortcuts import render
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from .models import Book, Category
from django.db.models import Q

def ahome(request):
        # 获取所有图书
    books = Book.objects.all().order_by('id')  # 默认按ID排序
    
    # 获取所有分类（用于筛选）
    categories = Category.objects.all()
    
    # 处理搜索
    search_query = request.GET.get('search', '')
    if search_query:
        books = books.filter(
            Q(name__icontains=search_query) | 
            Q(author__icontains=search_query) |
            Q(biy__icontains=search_query) |
            Q(cbs__icontains=search_query)
        )
    
    # 处理分类筛选
    category_filter = request.GET.get('category', '')
    if category_filter:
        books = books.filter(category__id=category_filter)
    
    # 处理年份筛选
    year_filter = request.GET.get('year', '')
    if year_filter:
        books = books.filter(nf=year_filter)
    
    # 分页 - 每页显示10条记录
    paginator = Paginator(books, 10)
    page = request.GET.get('page')
    
    try:
        books = paginator.page(page)
    except PageNotAnInteger:
        # 如果page不是整数，显示第一页
        books = paginator.page(1)
    except EmptyPage:
        # 如果page超出范围，显示最后一页
        books = paginator.page(paginator.num_pages)
    
    context = {
        'books': books,
        'categories': categories,
        'search_query': search_query,
        'selected_category': category_filter,
        'selected_year': year_filter,
    }
    return render(request,"ahome.html", context)


from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse
from django.contrib import messages


def book_delete(request, pk):
    book = get_object_or_404(Book, pk=pk)
    if request.method == 'POST':
        book.delete()
        print('图书删除成功')
        return redirect('ahome')
    return redirect('ahome')


from django.shortcuts import get_object_or_404, render, redirect
from django.urls import reverse
from .models import Book, Content
def abook_detail(request, pk):
    book = get_object_or_404(Book, pk=pk)
    if request.method == 'POST' and 'delete' in request.POST:
        content_id = request.POST.get('content_id')
        content = get_object_or_404(Content, id=content_id, book=book)
        content.delete()
        return redirect(reverse('book_detail', kwargs={'pk': pk}))
    
    if request.method == 'POST' and 'add' in request.POST:
        title = request.POST.get('title')
        content_text = request.POST.get('content')
        Content.objects.create(
            book=book,
            title=title,
            content=content_text
        )
        return redirect(reverse('book_detail', kwargs={'pk': pk}))
    
    if request.method == 'POST' and 'add_comment' in request.POST:
        content = request.POST.get('content')
        Bookcomment.objects.create(
            book=book,
            content=content
        )
        return redirect(reverse('book_detail', kwargs={'pk': pk}))
    
    contents = book.contents.all()
    
    return render(request, 'book_detail.html', {
        'book': book,
        'contents': contents
    })



def acontent_detail(request, book_id, content_id):
    book = get_object_or_404(Book, pk=book_id)
    content = get_object_or_404(Content, pk=content_id, book=book)
    return render(request, 'content_detail.html', {
        'book': book,
        'content': content
    })



from django.views.generic import UpdateView
from django.urls import reverse
from django.contrib import messages

class BookEditView(UpdateView):
    model = Book
    fields = [
        'bid', 'name', 'author', 'img_url', 'biy', 
        'description', 'price', 'nf', 'cbs', 'des', 
        'kcl', 'content', 'category'
    ]
    template_name = 'book_edit.html'
    context_object_name = 'book'
    
    def get_success_url(self):
        page = self.request.GET.get('page', '1')
        messages.success(self.request, '图书信息更新成功')
        return reverse('ahome')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.all()
        context['current_page'] = self.request.GET.get('page', '1')
        return context


from django import forms


class BookForm(forms.ModelForm):
    class Meta:
        model = Book
        fields = ['name', 'author', 'img_url', 'biy', 'description', 'price', 'nf', 'cbs', 'des', 'kcl', 'content', 'category']
        



def add_book(request):
    if request.method == 'POST':
        form = BookForm(request.POST, request.FILES)
        if form.is_valid():
            form.save()
            return redirect('ahome') 
        else:
            return render(request, 'add_book.html', {'form': form, 'error': '请检查输入信息'})
    else:
        form = BookForm()
    return render(request, 'add_book.html', {'form': form})




def aorder_list(request):
    orders = Order.objects.all()
    search_query = request.GET.get('search', '')
    
    if search_query:
        orders = orders.filter(order_num__icontains=search_query)  

    return render(request, 'aorder_list.html', {'orders': orders, 'search_query': search_query})


import json
from django.shortcuts import render
from .models import Order
import json
from django.shortcuts import render
from .models import Order

def aorder_detail(request, order_id):
    order = Order.objects.get(id=order_id)
    goods = json.loads(order.goods) 

    order_goods = []
    
    for item in goods:
        product_id = item['product_id']
        product_name = item['product_name']
        product_image = item['product_image']
        product_price = item['product_price']
        quantity = item['quantity']
        item_total = item['item_total']
        
        order_goods.append({
            'product_id': product_id,
            'product_name': product_name,
            'product_image': product_image,
            'product_price': product_price,
            'quantity': quantity,
            'item_total': item_total
        })

    return render(request, 'order_detail.html', {'order': order, 'order_goods': order_goods})






def adelete_order(request, order_id):
    order = Order.objects.get(id=order_id)
    order.delete()
    return redirect('aorder_list') 


from django import forms

class ReplyForm(forms.Form):
    reply = forms.CharField(widget=forms.Textarea(attrs={'rows': 5, 'cols': 60}), label="回复内容")


def afeed_back(request):
    search_query = request.GET.get('search', '')
    comments = Comment.objects.all()
    
    if search_query:
        comments = comments.filter(cont__icontains=search_query)
    
    return render(request, 'afeedback.html', {'comments': comments, 'search_query': search_query})

def edit_reply(request, comment_id):
    comment = get_object_or_404(Comment, id=comment_id)
    if request.method == 'GET':
        form = ReplyForm(initial={'reply': comment.reply})
    
    if request.method == 'POST':
        form = ReplyForm(request.POST)
        if form.is_valid():
            comment.reply = form.cleaned_data['reply']
            comment.save()
            return redirect('afeed_back')
    
    return render(request, 'edit_reply.html', {'form': form, 'comment': comment})



def delete_reply(request, comment_id):
    comment = Comment.objects.get(id=comment_id)
    comment.delete()
    return redirect('afeed_back') 

from django.shortcuts import render
from pyecharts.charts import Bar, Pie
from pyecharts.options import LabelOpts
from .models import User, Book, Order, Comment, Category
from django.db.models import Count
from pyecharts.globals import ThemeType
from pyecharts import options as opts
from django.db.models import Count, Sum
from pyecharts.charts import Bar, Pie, Funnel, Map
from pyecharts import options as opts
from pyecharts.globals import ThemeType
from decimal import Decimal
def a_data(request):
    user_count = User.objects.count()
    book_count = Book.objects.count()
    order_count = Order.objects.count()
    feedback_count = Comment.objects.count()

    category_counts = Book.objects.values('category__name').annotate(count=Count('category')).order_by('category__name')
    categories = [category['category__name'] for category in category_counts]
    category_values = [category['count'] for category in category_counts]
    
    bar = Bar(init_opts=opts.InitOpts(theme=ThemeType.LIGHT, width='100%', height='50vh'))
    bar.add_xaxis(categories)
    bar.add_yaxis("图书数量", category_values)
    bar.set_global_opts(
        title_opts={"text": "各图书分类数量"}
    )

    feedback_counts = Comment.objects.values('lx').annotate(count=Count('lx'))
    feedback_types = [item['lx'] for item in feedback_counts]
    feedback_type_counts = [item['count'] for item in feedback_counts]
    
    pie = Pie(init_opts=opts.InitOpts(theme=ThemeType.LIGHT, width='100%', height='40vh'))
    pie.add("", [list(z) for z in zip(feedback_types, feedback_type_counts)])
    pie.set_global_opts(
        title_opts={"text": "反馈问题类型分布"}
    )
    pie.set_series_opts(label_opts=opts.LabelOpts(formatter="{b}: {c} ({d}%)"))


    from collections import defaultdict
    book_sales = defaultdict(lambda: {'quantity': 0, 'revenue': Decimal('0.00')})
    
    for order in Order.objects.all():
        try:
            goods = json.loads(order.goods)
            for item in goods:
                book_id = item['product_id']
                book_sales[book_id]['quantity'] += item['quantity']
                book_sales[book_id]['revenue'] += Decimal(item['item_total'])
        except:
            continue
    top_books = sorted(
        [(book_id, data) for book_id, data in book_sales.items()],
        key=lambda x: x[1]['revenue'],
        reverse=True
    )[:10]
    
    book_names = {
        book.id: book.name 
        for book in Book.objects.filter(id__in=[item[0] for item in top_books])
    }
    
    funnel_data = [
        (book_names.get(book_id, f"图书ID:{book_id}"), float(revenue['revenue']))
        for book_id, revenue in top_books
    ]
    
    funnel = Funnel(init_opts=opts.InitOpts(theme=ThemeType.LIGHT, width='100%', height='50vh'))
    funnel.add(
        "销售额",
        data_pair=funnel_data,
        gap=2,
        tooltip_opts=opts.TooltipOpts(formatter="{a} <br/>{b}: {c} 元"),
        label_opts=opts.LabelOpts(position="inside")
    )
    funnel.set_global_opts(
        title_opts={"text": ""}
    )
    provinces = [
        profile.province.split()[0]
        for profile in Profile.objects.exclude(province='未知')
        if profile.province and profile.province != '未知'
    ]
    print(provinces)
    province_counts = defaultdict(int)
    for province in provinces:
        province_counts[province] += 1

    map_data = [(province, count) for province, count in province_counts.items()]

    
    china_map = Map(init_opts=opts.InitOpts(theme=ThemeType.LIGHT, width='100%', height='50vh'))
    china_map.add(
        "用户分布",
        map_data,
        "china",
        is_map_symbol_show=False,
        label_opts=opts.LabelOpts(is_show=True)
    )
    china_map.set_global_opts(
        title_opts={"text": "用户地域分布"},
        visualmap_opts=opts.VisualMapOpts(
            min_=0,
            max_=max(province_counts.values(), default=10),
            is_piecewise=True
        )
    )

    return render(request, 'data.html', {
        'user_count': user_count,
        'book_count': book_count,
        'order_count': order_count,
        'feedback_count': feedback_count,
        'bar_chart': bar.render_embed(),
        'pie_chart': pie.render_embed(),
        'funnel_chart': funnel.render_embed(),
        'china_map': china_map.render_embed(),
    })
