/* 容器样式 */
.container {
  width: 100%;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 书籍项样式 */
.book-item {
  width: 100%;
  display: flex;
  flex-direction: row;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 书籍图片样式 */
.book-image {
  width: 180rpx;
  height: 240rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

/* 书籍信息容器 */
.book-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 书籍标题 */
.book-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 作者样式 */
.book-author {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

/* 元信息样式 */
.book-meta {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 6rpx;
}

/* 价格样式 */
.book-price {
  font-size: 30rpx;
  color: #e64340;
  font-weight: bold;
  margin-top: 10rpx;
}

/* 空状态样式 */
.empty {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  font-size: 28rpx;
  color: #999;
}