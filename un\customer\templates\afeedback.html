{% extends 'base.html' %}
{% block content %}
{% load static %}

<style>


.container {
    max-width: 1500px;
    margin: 0 auto;
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-top: 80px;
}

h1 {
    color: #2c3e50;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

/* 搜索表单样式 */
form {
    margin-bottom: 25px;
    display: flex;
    gap: 10px;
}

input[type="text"] {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.3s;
}

input[type="text"]:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

button[type="submit"] {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

button[type="submit"]:hover {
    background-color: #2980b9;
}

/* 表格样式 */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

th {
    background-color: #f8f9fa;
    color: #2c3e50;
    font-weight: 600;
    position: sticky;
    top: 0;
}

tr:hover {
    background-color: #f8f9fa;
}

/* 操作链接样式 */
td a {
    color: #3498db;
    text-decoration: none;
    margin: 0 5px;
    transition: color 0.3s;
}

td a:hover {
    text-decoration: underline;
    color: #2980b9;
}

td a[style*="color:red"] {
    color: #e74c3c !important;
}

td a[style*="color:red"]:hover {
    color: #c0392b !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    table {
        display: block;
        overflow-x: auto;
    }
    
    form {
        flex-direction: column;
    }
    
    button[type="submit"] {
        width: 100%;
    }
}

/* 无回复提示样式 */
td p {
    margin: 0;
    color: #7f8c8d;
    font-style: italic;
}
</style>

<div class="container">

    
    <!-- 搜索框 -->
    <form method="GET">
        <input type="text" name="search" value="{{ search_query }}" placeholder="搜索反馈内容">
        <button type="submit">搜索</button>
    </form>
    
    <!-- 反馈表格 -->
    <table>
        <thead>
            <tr>
                <th>反馈类型</th>
                <th>用户</th>
                <th>反馈内容</th>
                <th>联系方式</th>
                <th>创建时间</th>
                <th>回复内容</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for comment in comments %}
            <tr>
                <td>{{ comment.get_lx_display }}</td>
                <td>{{ comment.user.username }}</td>
        <td>{{ comment.cont|truncatechars:20 }}</td>
                <td>{{ comment.email }}</td>
                <td>{{ comment.create_time }}</td>
                <td>
                    {% if comment.reply %}
                        <p>{{ comment.reply }}</p>
                    {% else %}
                        <p>暂无回复</p>
                    {% endif %}
                </td>
                <td>
                    {% if comment.reply %}
                        <a href="{% url 'edit_reply' comment.id %}">编辑回复</a> | 
                        <a href="{% url 'delete_reply' comment.id %}" style="color:red;">删除回复</a>
                    {% else %}
                        <a href="{% url 'edit_reply' comment.id %}">编辑回复</a>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>


{% endblock %}