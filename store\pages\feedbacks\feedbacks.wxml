<!-- pages/feedback-history/feedback-history.wxml -->
<view class="container">

  <view class="feedback-list">
    <block wx:for="{{feedbackList}}" wx:key="id">
      <view class="feedback-card">
        <!-- 卡片头部 - 反馈类型和日期 -->
        <view class="card-header">
          <view class="feedback-type {{item.lx}}">
            {{item.lx_display}}
          </view>
          <view class="feedback-date">
            {{item.create_time}}
          </view>
        </view>

        <!-- 卡片内容 - 反馈详情 -->
        <view class="card-content">
          <view class="feedback-content">
            反馈内容：{{item.cont}}
          </view>
        </view>

        <!-- 管理员回复 -->
        <view class="card-reply" wx:if="{{item.reply}}">
          <view class="reply-label">管理员回复：</view>
          <view class="reply-content">
          回复：{{item.reply}}
          </view>
        </view>

      </view>
    </block>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{feedbackList.length === 0}}">
    <text class="empty-text">暂无反馈记录</text>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}">
    <text wx:if="{{isLoading}}">加载中...</text>
    <text wx:else bindtap="loadMore">点击加载更多</text>
  </view>
</view>