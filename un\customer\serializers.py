# users/serializers.py
import json
import re
from rest_framework import serializers
from .models import User, Profile

from rest_framework import serializers
from .models import Profile
from django.contrib.auth.models import User

class ProfileSerializer(serializers.ModelSerializer):
    username = serializers.CharField(source='user.username', read_only=True)
    avatar_url = serializers.SerializerMethodField()

    class Meta:
        model = Profile
        fields = [
            'username',
            'moneys',
            'isvip',
            'sex',
            'phone',
            'address',
            'avatar',
            'avatar_url'
        ]
        extra_kwargs = {
            'moneys': {'read_only': True},
            'isvip': {'read_only': True},
            'avatar': {'write_only': True}
        }

    def get_avatar_url(self, obj):
        if obj.avatar:
            return self.context['request'].build_absolute_uri(obj.avatar.url)
        return None

    def validate_phone(self, value):
        """
        验证手机号格式
        """
        if value == '未知':
            return value
            
        # 简单的手机号格式验证
        if not value.isdigit() or len(value) < 6:
            raise serializers.ValidationError("请输入有效的手机号码")
        return value

    def validate_sex(self, value):
        """
        验证性别选项
        """
        if value not in [0, 1]:
            raise serializers.ValidationError("性别选择无效")
        return value

    def update(self, instance, validated_data):
        """
        更新用户资料
        """
        # 可以在这里添加额外的更新逻辑
        return super().update(instance, validated_data)

class UserSerializer(serializers.ModelSerializer):
    profile = ProfileSerializer(required=False)

    class Meta:
        model = User
        fields = ['username', 'email', 'password', 'profile']
        extra_kwargs = {'password': {'write_only': True}}

    def create(self, validated_data):
        profile_data = validated_data.pop('profile', {})
        user = User.objects.create_user(**validated_data)
        Profile.objects.create(user=user, **profile_data)
        return user




from rest_framework import serializers
from .models import Book

class BookSerializer(serializers.ModelSerializer):
    category = serializers.StringRelatedField()
    publisher = serializers.CharField(source='cbs')
    
    class Meta:
        model = Book
        fields = [
            'id', 'bid', 'name', 'author', 'img_url', 
            'price', 'nf', 'publisher', 'category', 'kcl',
            'description', 'content'
        ]




from rest_framework import serializers
from .models import Book

class BookDetailSerializer(serializers.ModelSerializer):
    category = serializers.StringRelatedField()
    publisher = serializers.CharField(source='cbs')
    stock = serializers.IntegerField(source='kcl')
    
    class Meta:
        model = Book
        fields = [
            'id', 'name', 'author', 'img_url', 
            'price', 'nf', 'publisher', 'category', 'stock',
            'description', 'content', 'biy', 'des'
        ]
        
    def to_representation(self, instance):
        data = super().to_representation(instance)
        # 确保价格是浮点数
        data['price'] = float(data['price']) if data['price'] else 0
        # 合并描述字段
        data['description'] = data['description'] or data['des']
        data.pop('des', None)
        return data


from rest_framework import serializers
from .models import Comment
from django.contrib.auth import get_user_model

User = get_user_model()

from rest_framework import serializers
from .models import Comment

class CommentSerializer(serializers.ModelSerializer):
    lx_display = serializers.CharField(source='get_lx_display', read_only=True)
    username = serializers.CharField(source='user.username', read_only=True)
    feedback_type = serializers.CharField(source='lx', write_only=True, required=False)

    class Meta:
        model = Comment
        fields = [
            'id', 'user', 'username', 'email', 'lx', 'lx_display',
            'cont', 'create_time', 'reply', 'feedback_type'
        ]
        read_only_fields = ['user', 'create_time', 'reply']

    def create(self, validated_data):
        # 自动关联当前登录用户
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class ReplySerializer(serializers.ModelSerializer):
    class Meta:
        model = Comment
        fields = ['reply']



from rest_framework import serializers
from .models import Category, Book

class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['id', 'name']

class Book1Serializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    
    class Meta:
        model = Book
        fields = ['id', 'bid', 'name', 'author', 'img_url', 'price', 
                 'category', 'category_name', 'biy', 'nf', 'cbs']
        

from rest_framework import serializers
from .models import Bookcomment

class BookCommentSerializer(serializers.ModelSerializer):
    username = serializers.CharField(source='user.username', read_only=True)

    class Meta:
        model = Bookcomment
        fields = ['id', 'content', 'username', 'create_time', 'user']
        read_only_fields = ['user', 'create_time', 'username']

    def create(self, validated_data):
        # 自动关联当前登录用户
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)