const app = getApp();
Page({
  data: {
    cart: {
      items: [],
      total_price: '0.00',
      total_items: 0
    }
  },
  
  onLoad: function() {
    this.loadCart();
  },
  
  onShow: function() {
    this.loadCart();
  },
  
  loadCart: function() {
    const that = this;
    wx.request({
      url: 'http://127.0.0.1:8000/cart_list/',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.accessToken,
        'content-type': 'application/json'
      },
      success(res) {
        if (res.data.code === 1) {
          that.setData({
            cart: {
              items: res.data.data.items,
              total_price: res.data.data.total_price,
              total_items: res.data.data.total_items
            }
          });
        } else {
          wx.showToast({
            title: res.data.msg,
            icon: 'none'
          });
        }
      },
      fail(err) {
        wx.showToast({
          title: '获取购物车失败',
          icon: 'none'
        });
      }
    });
  },
  
  
  removeItem: function(e) {
    const cartId = e.currentTarget.dataset.id;
    const that = this;
    
    wx.showModal({
      title: '提示',
      content: '确定要从购物车中删除此商品吗？',
      success(res) {
        if (res.confirm) {
          wx.request({
            url: `http://127.0.0.1:8000/cart/${cartId}/delete/`,
            method: 'POST',
            header: {
              'Authorization': 'Bearer ' + app.globalData.accessToken,
              'content-type': 'application/json'
            },
            success(res) {
              if (res.data.code === 1) {
                that.loadCart();
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
              } else {
                wx.showToast({
                  title: res.data.msg,
                  icon: 'none'
                });
              }
            },
            fail(err) {
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },
  
  placeOrder: function() {
    const that = this;
    wx.showModal({
      title: '确认订单',
      content: `总计: ¥${that.data.cart.total_price}`,
      success(res) {
        if (res.confirm) {
          wx.request({
            url: 'http://127.0.0.1:8000/orders/place/',
            method: 'POST',
            header: {
              'Authorization': 'Bearer ' + app.globalData.accessToken,
              'content-type': 'application/json'
            },
            success(res) {
              if (res.data.code === 1) {
                wx.showToast({
                  title: '下单成功',
                  icon: 'success'
                });
                setTimeout(() => {
                  wx.redirectTo({
                    url: `/pages/orders/detail?id=${res.data.data.order_id}`
                  });
                }, 1500);
              } else {
                wx.showToast({
                  title: res.data.msg,
                  icon: 'none'
                });
              }
            },
            fail(err) {
              wx.showToast({
                title: '下单失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },
  
  goToBookList: function() {
    wx.navigateTo({
      url: '/pages/book/book'
    })
  }
});